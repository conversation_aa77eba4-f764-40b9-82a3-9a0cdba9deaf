package com.grocease.service;

import com.grocease.dto.notification.SendNotificationRequest;
import com.grocease.entity.NotificationHistory;
import com.grocease.entity.Order;
import com.grocease.entity.User;
import com.grocease.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationTriggerService {

    private final NotificationService notificationService;
    private final UserRepository userRepository;

    @Async
    public void sendOrderStatusNotification(Order order, Order.OrderStatus newStatus) {
        try {
            String title = getOrderNotificationTitle(newStatus);
            String message = getOrderNotificationMessage(order, newStatus);
            NotificationHistory.NotificationType type = getOrderNotificationType(newStatus);

            if (title != null && message != null && type != null) {
                SendNotificationRequest request = SendNotificationRequest.builder()
                        .title(title)
                        .message(message)
                        .type(type)
                        .userIds(List.of(order.getUser().getId()))
                        .data(Map.of(
                                "orderId", order.getId().toString(),
                                "orderNumber", order.getOrderNumber(),
                                "status", newStatus.name()
                        ))
                        .isBroadcast(false)
                        .build();

                notificationService.sendNotification(request);
                log.info("Order status notification sent for order: {} to user: {}", 
                        order.getId(), order.getUser().getId());
            }
        } catch (Exception e) {
            log.error("Failed to send order status notification for order: {}", order.getId(), e);
        }
    }

    @Scheduled(cron = "0 0 9 * * *") // Daily at 9 AM
    public void sendBirthdayWishes() {
        try {
            LocalDate today = LocalDate.now();
            List<User> birthdayUsers = userRepository.findUsersByBirthDate(today);

            for (User user : birthdayUsers) {
                SendNotificationRequest request = SendNotificationRequest.builder()
                        .title("Happy Birthday! 🎉")
                        .message(String.format("Happy Birthday, %s! Enjoy 20%% off on your next order. Use code: BIRTHDAY20", 
                                user.getName()))
                        .type(NotificationHistory.NotificationType.BIRTHDAY_WISH)
                        .userIds(List.of(user.getId()))
                        .data(Map.of(
                                "discountCode", "BIRTHDAY20",
                                "discountPercentage", "20"
                        ))
                        .isBroadcast(false)
                        .build();

                if (notificationService.canSendNotification(user.getId())) {
                    notificationService.sendNotification(request);
                    log.info("Birthday notification sent to user: {}", user.getId());
                }
            }

            log.info("Birthday notifications processed for {} users", birthdayUsers.size());
        } catch (Exception e) {
            log.error("Failed to send birthday notifications", e);
        }
    }

    @Scheduled(cron = "0 0 10 * * MON") // Weekly on Monday at 10 AM
    public void sendWeeklyPromotionalOffers() {
        try {
            SendNotificationRequest request = SendNotificationRequest.builder()
                    .title("Weekly Deals Are Here! 🛒")
                    .message("Don't miss out on this week's special offers. Fresh produce at unbeatable prices!")
                    .type(NotificationHistory.NotificationType.PROMOTIONAL_OFFER)
                    .data(Map.of(
                            "offerType", "weekly_deals",
                            "validUntil", LocalDate.now().plusDays(7).toString()
                    ))
                    .isBroadcast(true)
                    .build();

            if (notificationService.canSendBroadcast()) {
                notificationService.sendNotification(request);
                log.info("Weekly promotional notification sent to all users");
            }
        } catch (Exception e) {
            log.error("Failed to send weekly promotional notifications", e);
        }
    }

    @Async
    public void sendNewProductNotification(String productName, String categoryName) {
        try {
            SendNotificationRequest request = SendNotificationRequest.builder()
                    .title("New Product Alert! 🆕")
                    .message(String.format("Check out our new %s in the %s category. Fresh arrivals just for you!", 
                            productName, categoryName))
                    .type(NotificationHistory.NotificationType.NEW_PRODUCT)
                    .data(Map.of(
                            "productName", productName,
                            "categoryName", categoryName
                    ))
                    .isBroadcast(true)
                    .build();

            if (notificationService.canSendBroadcast()) {
                notificationService.sendNotification(request);
                log.info("New product notification sent for: {}", productName);
            }
        } catch (Exception e) {
            log.error("Failed to send new product notification for: {}", productName, e);
        }
    }

    @Async
    public void sendLowStockAlert(Long userId, String productName) {
        try {
            SendNotificationRequest request = SendNotificationRequest.builder()
                    .title("Hurry! Low Stock Alert 📦")
                    .message(String.format("Only a few %s left in stock. Order now before it's gone!", productName))
                    .type(NotificationHistory.NotificationType.LOW_STOCK_ALERT)
                    .userIds(List.of(userId))
                    .data(Map.of(
                            "productName", productName,
                            "alertType", "low_stock"
                    ))
                    .isBroadcast(false)
                    .build();

            if (notificationService.canSendNotification(userId)) {
                notificationService.sendNotification(request);
                log.info("Low stock alert sent to user: {} for product: {}", userId, productName);
            }
        } catch (Exception e) {
            log.error("Failed to send low stock alert to user: {} for product: {}", userId, productName, e);
        }
    }

    @Async
    public void sendNewOrderNotificationToAdmins(Order order) {
        try {
            // Get all active admin users
            List<User> adminUsers = userRepository.findByRoleAndIsActiveTrue(User.Role.ADMIN);

            if (adminUsers.isEmpty()) {
                log.warn("No active admin users found to send new order notification");
                return;
            }

            List<Long> adminUserIds = adminUsers.stream()
                    .map(User::getId)
                    .collect(Collectors.toList());

            String customerName = order.getUser() != null ? order.getUser().getName() : "Unknown Customer";
            String title = "New Order Received! 🛒";
            String message = String.format("New order #%s from %s for Rs %s",
                    order.getOrderNumber(), customerName, order.getTotal());

            SendNotificationRequest request = SendNotificationRequest.builder()
                    .title(title)
                    .message(message)
                    .type(NotificationHistory.NotificationType.NEW_ORDER_ADMIN)
                    .userIds(adminUserIds)
                    .data(Map.of(
                            "orderId", order.getId().toString(),
                            "orderNumber", order.getOrderNumber(),
                            "customerName", customerName,
                            "total", order.getTotal().toString(),
                            "status", order.getStatus().name()
                    ))
                    .isBroadcast(false)
                    .build();

            notificationService.sendNotification(request);
            log.info("New order notification sent to {} admin users for order: {}",
                    adminUserIds.size(), order.getOrderNumber());
        } catch (Exception e) {
            log.error("Failed to send new order notification to admins for order: {}",
                    order.getOrderNumber(), e);
        }
    }

    @Scheduled(cron = "0 0 18 * * *") // Daily at 6 PM
    public void sendCartReminderNotifications() {
        try {
            // This would require implementing a cart/wishlist feature
            // For now, we'll send a general reminder to active users
            LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
            
            // In a real implementation, you would query for users with items in cart
            // but no recent orders
            log.info("Cart reminder notifications would be processed here");
            
            // Example implementation:
            /*
            List<User> usersWithAbandonedCarts = cartRepository.findUsersWithAbandonedCarts(threeDaysAgo);
            
            for (User user : usersWithAbandonedCarts) {
                SendNotificationRequest request = SendNotificationRequest.builder()
                        .title("Don't Forget Your Cart! 🛒")
                        .message("You have items waiting in your cart. Complete your order and get them delivered fresh!")
                        .type(NotificationHistory.NotificationType.CART_REMINDER)
                        .userIds(List.of(user.getId()))
                        .data(Map.of("reminderType", "abandoned_cart"))
                        .isBroadcast(false)
                        .build();

                if (notificationService.canSendNotification(user.getId())) {
                    notificationService.sendNotification(request);
                }
            }
            */
        } catch (Exception e) {
            log.error("Failed to send cart reminder notifications", e);
        }
    }

    private String getOrderNotificationTitle(Order.OrderStatus status) {
        return switch (status) {
            case CONFIRMED -> "Order Confirmed ✅";
            case PREPARING -> "Order Being Prepared 👨‍🍳";
            case OUT_FOR_DELIVERY -> "Order Out for Delivery 🚚";
            case DELIVERED -> "Order Delivered 📦";
            case CANCELLED -> "Order Cancelled ❌";
            default -> null;
        };
    }

    private String getOrderNotificationMessage(Order order, Order.OrderStatus status) {
        return switch (status) {
            case CONFIRMED -> String.format("Great news! Your order #%s has been confirmed and will be prepared soon.", 
                    order.getOrderNumber());
            case PREPARING -> String.format("Your order #%s is being carefully prepared by our team.", 
                    order.getOrderNumber());
            case OUT_FOR_DELIVERY -> String.format("Your order #%s is on its way to you! Expected delivery in 30-45 minutes.", 
                    order.getOrderNumber());
            case DELIVERED -> String.format("Your order #%s has been delivered successfully. Enjoy your fresh groceries!", 
                    order.getOrderNumber());
            case CANCELLED -> String.format("Your order #%s has been cancelled. If you have any questions, please contact our support team.", 
                    order.getOrderNumber());
            default -> null;
        };
    }

    private NotificationHistory.NotificationType getOrderNotificationType(Order.OrderStatus status) {
        return switch (status) {
            case CONFIRMED -> NotificationHistory.NotificationType.ORDER_CONFIRMATION;
            case PREPARING -> NotificationHistory.NotificationType.ORDER_PREPARING;
            case OUT_FOR_DELIVERY -> NotificationHistory.NotificationType.ORDER_OUT_FOR_DELIVERY;
            case DELIVERED -> NotificationHistory.NotificationType.ORDER_DELIVERED;
            case CANCELLED -> NotificationHistory.NotificationType.ORDER_CANCELLED;
            default -> null;
        };
    }
}
