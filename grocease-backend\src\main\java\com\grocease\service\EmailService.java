package com.grocease.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final JavaMailSender mailSender;

    @Value("${spring.mail.username:}")
    private String mailUsername;

    public void sendEmail(String to, String subject, String text) {
        try {
            log.info("Attempting to send email to: {} with subject: {}", to, subject);
            log.info("Email content: {}", text);

            // Check if email is configured
            if (mailUsername == null || mailUsername.trim().isEmpty()) {
                log.warn("Email service not configured (MAIL_USERNAME is empty)");
                log.warn("=== DEVELOPMENT MODE: EMAIL NOT SENT ===");
                log.warn("To: {}", to);
                log.warn("Subject: {}", subject);
                log.warn("Content: {}", text);
                log.warn("=== END EMAIL CONTENT ===");

                // Extract OTP from content for easy testing
                if (text.contains("OTP")) {
                    String[] lines = text.split("\n");
                    for (String line : lines) {
                        if (line.contains("OTP") && line.contains(":")) {
                            log.warn("*** DEVELOPMENT OTP FOR TESTING: {} ***", line.split(":")[1].trim().split(" ")[0]);
                            break;
                        }
                    }
                }
                return;
            }

            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(to);
            message.setSubject(subject);
            message.setText(text);
            message.setFrom("<EMAIL>");

            mailSender.send(message);
            log.info("Email sent successfully to: {}", to);
        } catch (Exception e) {
            log.error("Failed to send email to: {}. Error: {}", to, e.getMessage(), e);
            // For development, we'll log the OTP instead of failing
            log.warn("EMAIL SENDING FAILED - For development, check the OTP in logs above");
        }
    }
}
