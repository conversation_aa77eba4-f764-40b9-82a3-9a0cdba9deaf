package com.grocease.service;

import com.grocease.entity.*;
import com.grocease.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class DataInitializationService implements CommandLineRunner {

    private final UserRepository userRepository;
    private final OrderRepository orderRepository;
    private final ProductRepository productRepository;
    private final AddressRepository addressRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        // Create sample users if none exist
        if (userRepository.count() == 0) {
            log.info("No users found, creating sample users for testing...");
            createSampleUsers();
        }

        // Create sample orders if none exist
        if (orderRepository.count() == 0) {
            log.info("No orders found, creating sample orders for testing...");
            createSampleOrders();
        }
    }

    private void createSampleUsers() {
        try {
            // Create sample users for testing
            createSampleUser("John Doe", "<EMAIL>", "password123", "1234567890");
            createSampleUser("Jane Smith", "<EMAIL>", "password123", "0987654321");
            createSampleUser("Bob Johnson", "<EMAIL>", "password123", "5555555555");

            // Create admin user
            createAdminUser("Admin User", "<EMAIL>", "admin123", "1111111111");

            log.info("Sample users created successfully");
        } catch (Exception e) {
            log.error("Error creating sample users: {}", e.getMessage(), e);
        }
    }

    private void createSampleUser(String name, String email, String password, String phone) {
        User user = User.builder()
                .name(name)
                .email(email)
                .password(passwordEncoder.encode(password))
                .phone(phone)
                .isEmailVerified(true)
                .isActive(true)
                .role(User.Role.USER)
                .build();
        userRepository.save(user);
        log.info("Created sample user: {}", email);
    }

    private void createAdminUser(String name, String email, String password, String phone) {
        User user = User.builder()
                .name(name)
                .email(email)
                .password(passwordEncoder.encode(password))
                .phone(phone)
                .isEmailVerified(true)
                .isActive(true)
                .role(User.Role.ADMIN)
                .build();
        userRepository.save(user);
        log.info("Created admin user: {}", email);
    }

    private void createSampleOrders() {
        try {
            // Get some products first
            List<Product> products = productRepository.findAll();
            if (products.isEmpty()) {
                log.warn("No products found, cannot create sample orders");
                return;
            }

            // Get all existing users and create orders for them
            List<User> existingUsers = userRepository.findAll();
            if (!existingUsers.isEmpty()) {
                log.info("Found {} existing users, creating orders for them", existingUsers.size());
                for (User user : existingUsers) {
                    if (user.getRole() == User.Role.USER) { // Only create orders for regular users, not admins
                        createOrdersForUser(user, products);
                    }
                }
            }

            // Also create a test user with orders
            User testUser = userRepository.findByEmail("<EMAIL>")
                    .orElseGet(() -> createTestUser());
            createOrdersForUser(testUser, products);

            log.info("Sample orders created successfully");
        } catch (Exception e) {
            log.error("Error creating sample orders: {}", e.getMessage(), e);
        }
    }

    private void createOrdersForUser(User user, List<Product> products) {
        // Create a test address if not exists
        Address userAddress = addressRepository.findByUserIdAndIsDefaultTrue(user.getId())
                .orElseGet(() -> createTestAddress(user));

        // Create sample orders with different statuses and dates spread across last 6 months
        String userPrefix = user.getEmail().split("@")[0].toUpperCase();

        // Create orders for the last 6 months to show trends
        for (int monthsAgo = 5; monthsAgo >= 0; monthsAgo--) {
            LocalDateTime orderDate = LocalDateTime.now().minusMonths(monthsAgo);

            // Create 2-4 orders per month with varying amounts
            int ordersThisMonth = 2 + (int)(Math.random() * 3); // 2-4 orders

            for (int i = 1; i <= ordersThisMonth; i++) {
                Order.OrderStatus status = getRandomOrderStatus();
                BigDecimal orderTotal = generateRandomOrderTotal();

                String orderNumber = String.format("%s-%02d%02d", userPrefix, monthsAgo, i);
                createSampleOrderWithDate(user, userAddress, products, status, orderNumber, orderDate.plusDays(i * 7), orderTotal);
            }
        }

        log.info("Created sample orders for user: {}", user.getEmail());
    }

    private Order.OrderStatus getRandomOrderStatus() {
        Order.OrderStatus[] statuses = {
            Order.OrderStatus.DELIVERED,
            Order.OrderStatus.DELIVERED,
            Order.OrderStatus.DELIVERED, // More delivered orders
            Order.OrderStatus.CONFIRMED,
            Order.OrderStatus.PREPARING,
            Order.OrderStatus.OUT_FOR_DELIVERY,
            Order.OrderStatus.CANCELLED
        };
        return statuses[(int)(Math.random() * statuses.length)];
    }

    private BigDecimal generateRandomOrderTotal() {
        // Generate random order total between Rs 200 and Rs 1500
        double min = 200.0;
        double max = 1500.0;
        double randomTotal = min + (Math.random() * (max - min));
        return BigDecimal.valueOf(Math.round(randomTotal * 100.0) / 100.0);
    }

    private User createTestUser() {
        User user = User.builder()
                .name("Test User")
                .email("<EMAIL>")
                .password(passwordEncoder.encode("password123"))
                .phone("1234567890")
                .isEmailVerified(true)
                .isActive(true)
                .role(User.Role.USER)
                .build();
        return userRepository.save(user);
    }

    private Address createTestAddress(User user) {
        Address address = Address.builder()
                .type(Address.AddressType.HOME)
                .street("123 Test Street")
                .city("Test City")
                .state("Test State")
                .zipCode("12345")
                .isDefault(true)
                .user(user)
                .build();
        return addressRepository.save(address);
    }

    private void createSampleOrderWithDate(User user, Address address, List<Product> products,
                                          Order.OrderStatus status, String orderNumber, LocalDateTime orderDate, BigDecimal total) {
        BigDecimal deliveryFee = BigDecimal.valueOf(50.00); // Rs 50 delivery fee
        BigDecimal subtotal = total.subtract(deliveryFee);

        // Create order with specific date
        Order order = Order.builder()
                .orderNumber(orderNumber)
                .total(total)
                .subtotal(subtotal)
                .deliveryFee(deliveryFee)
                .discount(BigDecimal.ZERO)
                .status(status)
                .estimatedDelivery(orderDate.plusHours(2))
                .user(user)
                .deliveryAddress(address)
                .items(new ArrayList<>())
                .build();

        // Set the creation date manually
        order.setCreatedAt(orderDate);
        order.setUpdatedAt(orderDate);

        if (status == Order.OrderStatus.DELIVERED) {
            order.setDeliveredAt(orderDate.plusHours(3));
        }

        Order savedOrder = orderRepository.save(order);

        // Add some order items
        if (!products.isEmpty()) {
            // Calculate item prices to match the total
            int numItems = Math.min(products.size(), 3); // Max 3 items per order
            BigDecimal itemTotal = subtotal.divide(BigDecimal.valueOf(numItems), 2, RoundingMode.HALF_UP);

            for (int i = 0; i < numItems; i++) {
                Product product = products.get(i);
                int quantity = 1 + (int)(Math.random() * 3); // 1-3 quantity
                BigDecimal unitPrice = itemTotal.divide(BigDecimal.valueOf(quantity), 2, RoundingMode.HALF_UP);

                OrderItem item = OrderItem.builder()
                        .order(savedOrder)
                        .product(product)
                        .quantity(quantity)
                        .unitPrice(unitPrice)
                        .totalPrice(unitPrice.multiply(BigDecimal.valueOf(quantity)))
                        .build();

                savedOrder.getItems().add(item);
            }

            orderRepository.save(savedOrder);
        }

        log.info("Created sample order: {} with status: {} for date: {}", orderNumber, status, orderDate.toLocalDate());
    }

    private void createSampleOrder(User user, Address address, List<Product> products,
                                 Order.OrderStatus status, String orderNumber) {
        createSampleOrderWithDate(user, address, products, status, orderNumber, LocalDateTime.now(), BigDecimal.valueOf(450.00));
    }

    @Transactional
    public void regenerateSampleData() {
        log.info("Regenerating sample data for analytics testing...");

        try {
            // Clear existing orders
            orderRepository.deleteAll();
            log.info("Cleared existing orders");

            // Recreate sample orders
            createSampleOrders();
            log.info("Sample data regeneration completed successfully");
        } catch (Exception e) {
            log.error("Error during sample data regeneration: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to regenerate sample data", e);
        }
    }
}
