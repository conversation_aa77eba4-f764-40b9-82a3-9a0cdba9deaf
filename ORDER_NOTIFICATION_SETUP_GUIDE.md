# 🔔 Complete Order Notification Setup Guide

This guide provides step-by-step instructions for setting up order update notifications in both the admin panel backend and the mobile app.

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Prerequisites](#prerequisites)
3. [Backend Setup](#backend-setup)
4. [Mobile App Setup](#mobile-app-setup)
5. [Admin Panel Setup](#admin-panel-setup)
6. [Testing Guide](#testing-guide)
7. [Troubleshooting](#troubleshooting)

## 🏗️ System Overview

The notification system consists of:

- **Backend**: Spring Boot with Firebase Cloud Messaging (FCM)
- **Mobile App**: React Native with Expo Notifications
- **Admin Panel**: React/Next.js with real-time order management
- **Database**: PostgreSQL for notification history and device tokens

### Notification Flow

```
Order Status Update → NotificationTriggerService → NotificationService → FCM → Mobile App
                   ↓
Admin Panel ← WebSocket/Polling ← OrderService ← Database
```

## 🔧 Prerequisites

### Required Tools & Services

1. **Firebase Project** with FCM enabled
2. **Google Service Account** with FCM permissions
3. **PostgreSQL Database** (already configured)
4. **Expo Development Build** (for push notifications)

### Environment Variables

```bash
# Backend (.env or application.yml)
FIREBASE_ENABLED=true
FIREBASE_CONFIG_FILE=firebase-service-account.json

# Mobile App (.env)
EXPO_PROJECT_ID=your-expo-project-id
```

## 🚀 Backend Setup

### Step 1: Firebase Configuration

1. **Create Firebase Project**
   ```bash
   # Go to https://console.firebase.google.com/
   # Create new project or use existing one
   # Enable Cloud Messaging API
   ```

2. **Generate Service Account Key**
   ```bash
   # In Firebase Console:
   # Project Settings → Service Accounts → Generate New Private Key
   # Download the JSON file
   ```

3. **Configure Service Account**
   ```bash
   # Copy the downloaded JSON to:
   cp path/to/downloaded/key.json grocease-backend/src/main/resources/firebase-service-account.json
   ```

### Step 2: Backend Configuration

1. **Update application.yml**
   ```yaml
   firebase:
     enabled: true
     config:
       file: firebase-service-account.json
   ```

2. **Verify Database Tables**
   ```sql
   -- These tables should already exist from migrations
   SELECT * FROM user_device_tokens;
   SELECT * FROM notification_history;
   SELECT * FROM orders;
   ```

### Step 3: Test Backend Services

1. **Start the Backend**
   ```bash
   cd grocease-backend
   ./mvnw spring-boot:run
   ```

2. **Verify Firebase Connection**
   ```bash
   # Check logs for:
   # "Firebase application initialized successfully"
   ```

### Step 4: API Endpoints Verification

The following endpoints should be available:

```http
# Register device token
POST /notifications/register-token
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {jwt-token}

deviceToken=expo-push-token
deviceType=android
deviceId=device-unique-id

# Update order status (triggers notification)
PUT /admin/orders/{orderId}/status
Content-Type: application/json
Authorization: Bearer {admin-jwt-token}

{
  "status": "CONFIRMED",
  "notes": "Order confirmed by admin"
}

# Send custom notification
POST /admin/notifications/send
Content-Type: application/json
Authorization: Bearer {admin-jwt-token}

{
  "title": "Order Update",
  "message": "Your order status has been updated",
  "userIds": [1, 2, 3],
  "type": "ORDER_UPDATE"
}
```

## 📱 Mobile App Setup

### Step 1: Install Dependencies

```bash
cd grocease
npm install expo-notifications
```

### Step 2: Configure Expo Notifications

1. **Update app.json**
   ```json
   {
     "expo": {
       "plugins": [
         [
           "expo-notifications",
           {
             "icon": "./assets/notification-icon.png",
             "color": "#ffffff",
             "sounds": ["./assets/notification-sound.wav"]
           }
         ]
       ]
     }
   }
   ```

### Step 3: Implement Notification Service

1. **Create NotificationService**
   ```typescript
   // src/services/notificationService.ts
   import * as Notifications from 'expo-notifications';
   import { apiClient } from './api';

   export class NotificationService {
     static async registerForPushNotifications(): Promise<string | null> {
       // Implementation already exists in useNotifications.ts
     }

     static async registerDeviceToken(token: string): Promise<void> {
       await apiClient.post('/notifications/register-token', {
         deviceToken: token,
         deviceType: Platform.OS,
         deviceId: await getUniqueId()
       });
     }
   }
   ```

### Step 4: Update App.tsx

```typescript
// App.tsx
import { useNotifications } from './src/hooks/useNotifications';

export default function App() {
  const { expoPushToken, requestPermission } = useNotifications();

  useEffect(() => {
    if (expoPushToken) {
      // Register token with backend
      NotificationService.registerDeviceToken(expoPushToken);
    }
  }, [expoPushToken]);

  // Rest of your app...
}
```

### Step 5: Handle Notification Responses

```typescript
// src/hooks/useNotifications.ts (update existing)
useEffect(() => {
  responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
    const data = response.notification.request.content.data;
    
    if (data?.orderId) {
      // Navigate to order details
      navigation.navigate('OrderDetails', { orderId: data.orderId });
    }
  });
}, []);
```

## 🖥️ Admin Panel Setup

### Step 1: Real-time Order Updates

1. **Install WebSocket Client** (if not using polling)
   ```bash
   cd grocease-admin-panel
   npm install socket.io-client
   ```

2. **Update Order Management**
   ```typescript
   // src/hooks/useOrderUpdates.ts
   import { useQuery, useQueryClient } from '@tanstack/react-query';

   export const useOrderUpdates = () => {
     const queryClient = useQueryClient();

     // Poll for new orders every 30 seconds
     useQuery({
       queryKey: ['orders', 'updates'],
       queryFn: () => apiClient.getOrders(0, 10),
       refetchInterval: 30000,
       onSuccess: (data) => {
         // Check for new orders and show notification
         checkForNewOrders(data);
       }
     });
   };
   ```

### Step 2: Browser Notifications

```typescript
// src/hooks/useBrowserNotifications.ts
export const useBrowserNotifications = () => {
  const [permission, setPermission] = useState<NotificationPermission>('default');

  const requestPermission = async () => {
    const result = await Notification.requestPermission();
    setPermission(result);
    return result === 'granted';
  };

  const showNotification = (title: string, body: string) => {
    if (permission === 'granted') {
      new Notification(title, {
        body,
        icon: '/favicon.ico',
        badge: '/badge-icon.png'
      });
    }
  };

  return { permission, requestPermission, showNotification };
};
```

### Step 3: Order Status Update Component

```typescript
// src/components/OrderStatusUpdate.tsx
const OrderStatusUpdate = ({ order }: { order: Order }) => {
  const updateStatusMutation = useMutation({
    mutationFn: ({ status, notes }: { status: OrderStatus; notes?: string }) =>
      apiClient.updateOrderStatus(order.id, status, notes),
    onSuccess: () => {
      queryClient.invalidateQueries(['orders']);
      // Show success notification
      toast.success('Order status updated successfully');
    }
  });

  return (
    <Select
      value={order.status}
      onValueChange={(status) => updateStatusMutation.mutate({ status })}
    >
      <SelectItem value="CONFIRMED">Confirmed</SelectItem>
      <SelectItem value="PREPARING">Preparing</SelectItem>
      <SelectItem value="OUT_FOR_DELIVERY">Out for Delivery</SelectItem>
      <SelectItem value="DELIVERED">Delivered</SelectItem>
      <SelectItem value="CANCELLED">Cancelled</SelectItem>
    </Select>
  );
};
```

## 🧪 Testing Guide

### Backend Testing

1. **Test Firebase Connection**
   ```bash
   curl -X POST http://localhost:8080/admin/notifications/send \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "title": "Test Notification",
       "message": "Testing FCM integration",
       "userIds": [1]
     }'
   ```

2. **Test Order Status Update**
   ```bash
   curl -X PUT http://localhost:8080/admin/orders/1/status \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "status": "CONFIRMED",
       "notes": "Test status update"
     }'
   ```

### Mobile App Testing

1. **Test Device Registration**
   - Launch app
   - Check logs for "Device token registered successfully"
   - Verify token in database: `SELECT * FROM user_device_tokens;`

2. **Test Notification Reception**
   - Update order status from admin panel
   - Check if notification appears on device
   - Tap notification and verify navigation

### Admin Panel Testing

1. **Test Real-time Updates**
   - Create new order from mobile app
   - Check if admin panel shows notification
   - Verify order appears in orders list

2. **Test Status Updates**
   - Update order status from admin panel
   - Verify mobile app receives notification
   - Check notification history in database

## 🔧 Troubleshooting

### Common Issues

1. **Firebase Not Initialized**
   ```
   Error: Firebase messaging not available
   Solution: Check firebase-service-account.json exists and is valid
   ```

2. **Device Token Not Registered**
   ```
   Error: No active device tokens found
   Solution: Ensure mobile app calls /notifications/register-token
   ```

3. **Notifications Not Received**
   ```
   Check: 
   - Device permissions granted
   - Firebase project configuration
   - Network connectivity
   - Token validity
   ```

4. **Admin Panel Not Updating**
   ```
   Check:
   - API endpoints responding
   - Authentication tokens valid
   - WebSocket/polling configuration
   ```

### Debug Commands

```bash
# Check notification history
SELECT * FROM notification_history ORDER BY created_at DESC LIMIT 10;

# Check active device tokens
SELECT u.name, udt.device_token, udt.is_active 
FROM user_device_tokens udt 
JOIN users u ON udt.user_id = u.id 
WHERE udt.is_active = true;

# Check recent orders
SELECT id, order_number, status, created_at 
FROM orders 
ORDER BY created_at DESC LIMIT 10;
```

## 📚 Additional Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Expo Notifications Documentation](https://docs.expo.dev/versions/latest/sdk/notifications/)
- [Spring Boot Firebase Integration](https://firebase.google.com/docs/admin/setup#java)

## 📁 Implementation Files

### Backend Files to Create/Update

1. **NotificationWebSocketHandler.java** (Optional - for real-time admin updates)
   ```java
   @Component
   @Slf4j
   public class NotificationWebSocketHandler extends TextWebSocketHandler {

       private final Set<WebSocketSession> adminSessions = ConcurrentHashMap.newKeySet();

       @Override
       public void afterConnectionEstablished(WebSocketSession session) {
           adminSessions.add(session);
           log.info("Admin WebSocket connection established: {}", session.getId());
       }

       @Override
       public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
           adminSessions.remove(session);
           log.info("Admin WebSocket connection closed: {}", session.getId());
       }

       public void notifyAdminsOfNewOrder(Order order) {
           String message = String.format("{\"type\":\"NEW_ORDER\",\"orderId\":\"%s\",\"orderNumber\":\"%s\"}",
                   order.getId(), order.getOrderNumber());

           adminSessions.forEach(session -> {
               try {
                   session.sendMessage(new TextMessage(message));
               } catch (Exception e) {
                   log.error("Failed to send WebSocket message", e);
               }
           });
       }
   }
   ```

2. **Enhanced NotificationTriggerService.java**
   ```java
   @Service
   @RequiredArgsConstructor
   @Slf4j
   public class NotificationTriggerService {

       private final NotificationService notificationService;
       private final UserRepository userRepository;
       private final NotificationWebSocketHandler webSocketHandler; // Optional

       @Async
       public void sendOrderStatusNotification(Order order, Order.OrderStatus newStatus) {
           // Existing implementation...

           // Additionally notify admins via WebSocket
           if (webSocketHandler != null) {
               webSocketHandler.notifyAdminsOfOrderUpdate(order, newStatus);
           }
       }

       @Async
       public void sendNewOrderNotificationToAdmins(Order order) {
           // Existing FCM notification implementation...

           // Additionally notify via WebSocket for real-time updates
           if (webSocketHandler != null) {
               webSocketHandler.notifyAdminsOfNewOrder(order);
           }
       }
   }
   ```

### Mobile App Files to Create/Update

1. **src/services/notificationService.ts**
   ```typescript
   import * as Notifications from 'expo-notifications';
   import * as Device from 'expo-device';
   import { Platform } from 'react-native';
   import { apiClient } from './api';
   import AsyncStorage from '@react-native-async-storage/async-storage';

   export class NotificationService {
     private static TOKEN_KEY = 'expo_push_token';

     static async initialize(): Promise<void> {
       // Configure notification behavior
       Notifications.setNotificationHandler({
         handleNotification: async () => ({
           shouldShowAlert: true,
           shouldPlaySound: true,
           shouldSetBadge: true,
         }),
       });

       // Register for push notifications
       const token = await this.registerForPushNotifications();
       if (token) {
         await this.registerDeviceToken(token);
       }
     }

     static async registerForPushNotifications(): Promise<string | null> {
       if (!Device.isDevice) {
         console.log('Must use physical device for Push Notifications');
         return null;
       }

       const { status: existingStatus } = await Notifications.getPermissionsAsync();
       let finalStatus = existingStatus;

       if (existingStatus !== 'granted') {
         const { status } = await Notifications.requestPermissionsAsync();
         finalStatus = status;
       }

       if (finalStatus !== 'granted') {
         console.log('Failed to get push token for push notification!');
         return null;
       }

       const token = (await Notifications.getExpoPushTokenAsync()).data;
       await AsyncStorage.setItem(this.TOKEN_KEY, token);

       if (Platform.OS === 'android') {
         Notifications.setNotificationChannelAsync('default', {
           name: 'default',
           importance: Notifications.AndroidImportance.MAX,
           vibrationPattern: [0, 250, 250, 250],
           lightColor: '#FF231F7C',
         });
       }

       return token;
     }

     static async registerDeviceToken(token: string): Promise<void> {
       try {
         const deviceId = await Device.getDeviceIdAsync();
         await apiClient.post('/notifications/register-token', {
           deviceToken: token,
           deviceType: Platform.OS,
           deviceId: deviceId
         });
         console.log('Device token registered successfully');
       } catch (error) {
         console.error('Failed to register device token:', error);
       }
     }

     static async handleNotificationResponse(response: Notifications.NotificationResponse): Promise<void> {
       const data = response.notification.request.content.data;

       if (data?.orderId) {
         // Navigate to order details
         // This should be implemented based on your navigation structure
         console.log('Navigate to order:', data.orderId);
       }
     }
   }
   ```

2. **src/hooks/useOrderNotifications.ts**
   ```typescript
   import { useEffect, useRef } from 'react';
   import * as Notifications from 'expo-notifications';
   import { useNavigation } from '@react-navigation/native';
   import { NotificationService } from '../services/notificationService';

   export const useOrderNotifications = () => {
     const navigation = useNavigation();
     const notificationListener = useRef<Notifications.Subscription>();
     const responseListener = useRef<Notifications.Subscription>();

     useEffect(() => {
       // Initialize notification service
       NotificationService.initialize();

       // Listen for notifications received while app is foregrounded
       notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
         console.log('Notification received:', notification);

         // Show in-app notification or update UI
         const data = notification.request.content.data;
         if (data?.type === 'ORDER_UPDATE') {
           // Handle order update notification
           console.log('Order update received:', data);
         }
       });

       // Listen for user interactions with notifications
       responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
         NotificationService.handleNotificationResponse(response);

         const data = response.notification.request.content.data;
         if (data?.orderId) {
           navigation.navigate('OrderDetails', { orderId: data.orderId });
         }
       });

       return () => {
         if (notificationListener.current) {
           Notifications.removeNotificationSubscription(notificationListener.current);
         }
         if (responseListener.current) {
           Notifications.removeNotificationSubscription(responseListener.current);
         }
       };
     }, [navigation]);
   };
   ```

### Admin Panel Files to Create/Update

1. **src/hooks/useRealtimeOrders.ts**
   ```typescript
   import { useEffect, useState } from 'react';
   import { useQueryClient } from '@tanstack/react-query';
   import { Order } from '@/types';

   export const useRealtimeOrders = () => {
     const [socket, setSocket] = useState<WebSocket | null>(null);
     const queryClient = useQueryClient();

     useEffect(() => {
       // Connect to WebSocket for real-time updates
       const ws = new WebSocket('ws://localhost:8080/ws/admin');

       ws.onopen = () => {
         console.log('WebSocket connected');
         setSocket(ws);
       };

       ws.onmessage = (event) => {
         const data = JSON.parse(event.data);

         if (data.type === 'NEW_ORDER') {
           // Invalidate orders query to refetch
           queryClient.invalidateQueries(['orders']);

           // Show browser notification
           if (Notification.permission === 'granted') {
             new Notification('New Order Received!', {
               body: `Order #${data.orderNumber} has been placed`,
               icon: '/favicon.ico'
             });
           }
         }

         if (data.type === 'ORDER_UPDATE') {
           // Update specific order in cache
           queryClient.invalidateQueries(['order', data.orderId]);
           queryClient.invalidateQueries(['orders']);
         }
       };

       ws.onclose = () => {
         console.log('WebSocket disconnected');
         setSocket(null);
       };

       ws.onerror = (error) => {
         console.error('WebSocket error:', error);
       };

       return () => {
         ws.close();
       };
     }, [queryClient]);

     return { socket, isConnected: socket?.readyState === WebSocket.OPEN };
   };
   ```

2. **src/components/OrderNotificationBell.tsx**
   ```typescript
   import React, { useState, useEffect } from 'react';
   import { Bell, BellRing } from 'lucide-react';
   import { Button } from '@/components/ui/button';
   import { Badge } from '@/components/ui/badge';
   import { useRealtimeOrders } from '@/hooks/useRealtimeOrders';

   export const OrderNotificationBell = () => {
     const [unreadCount, setUnreadCount] = useState(0);
     const [hasNewOrder, setHasNewOrder] = useState(false);
     const { isConnected } = useRealtimeOrders();

     useEffect(() => {
       // Request browser notification permission
       if (Notification.permission === 'default') {
         Notification.requestPermission();
       }
     }, []);

     const handleBellClick = () => {
       setUnreadCount(0);
       setHasNewOrder(false);
       // Navigate to orders page or open orders modal
     };

     return (
       <div className="relative">
         <Button
           variant="ghost"
           size="icon"
           onClick={handleBellClick}
           className={`relative ${hasNewOrder ? 'animate-pulse' : ''}`}
         >
           {hasNewOrder ? (
             <BellRing className="h-5 w-5 text-orange-500" />
           ) : (
             <Bell className="h-5 w-5" />
           )}

           {unreadCount > 0 && (
             <Badge
               variant="destructive"
               className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
             >
               {unreadCount > 99 ? '99+' : unreadCount}
             </Badge>
           )}
         </Button>

         {/* Connection status indicator */}
         <div className={`absolute bottom-0 right-0 w-2 h-2 rounded-full ${
           isConnected ? 'bg-green-500' : 'bg-red-500'
         }`} />
       </div>
     );
   };
   ```

## 🔄 Deployment Checklist

### Backend Deployment

- [ ] Firebase service account JSON file in production
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] FCM API enabled in Firebase Console
- [ ] SSL certificates for WebSocket connections (if using)

### Mobile App Deployment

- [ ] Expo build configured for push notifications
- [ ] App store permissions for notifications
- [ ] Production API endpoints configured
- [ ] Notification icons and sounds included

### Admin Panel Deployment

- [ ] WebSocket endpoint configured for production
- [ ] Browser notification permissions handled
- [ ] Real-time connection fallback implemented
- [ ] Error handling for connection failures

---

**Next Steps**: After completing this setup, consider implementing advanced features like notification scheduling, user preferences, and analytics tracking.
