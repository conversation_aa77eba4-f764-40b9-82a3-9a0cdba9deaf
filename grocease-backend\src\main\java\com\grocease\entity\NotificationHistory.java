package com.grocease.entity;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "notification_history")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationHistory extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @Column(nullable = false)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String message;

    @Enumerated(EnumType.STRING)
    private NotificationType type;

    @Column(name = "device_token")
    private String deviceToken;

    @Enumerated(EnumType.STRING)
    private NotificationStatus status;

    @Column(name = "firebase_message_id")
    private String firebaseMessageId;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "is_broadcast")
    private Boolean isBroadcast = false;

    public enum NotificationType {
        ORDER_CONFIRMATION,
        ORDER_PREPARING,
        ORDER_OUT_FOR_DELIVERY,
        ORDER_DELIVERED,
        ORDER_CANCELLED,
        NEW_ORDER_ADMIN,
        BIRTHDAY_WISH,
        PROMOTIONAL_OFFER,
        NEW_PRODUCT,
        LOW_STOCK_ALERT,
        CART_REMINDER,
        GENERAL
    }

    public enum NotificationStatus {
        PENDING,
        SENT,
        DELIVERED,
        FAILED
    }
}
