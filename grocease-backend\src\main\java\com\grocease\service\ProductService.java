package com.grocease.service;

import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.product.CategoryDto;
import com.grocease.dto.product.CreateCategoryRequest;
import com.grocease.dto.product.CreateProductRequest;
import com.grocease.dto.product.ProductDto;
import com.grocease.entity.Category;
import com.grocease.entity.Product;
import com.grocease.entity.ProductTag;
import com.grocease.exception.ResourceNotFoundException;
import com.grocease.repository.CategoryRepository;
import com.grocease.repository.ProductRepository;

import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductService {

    private final ProductRepository productRepository;
    private final CategoryRepository categoryRepository;
    private final DtoMapper dtoMapper;

    public List<CategoryDto> getAllCategories() {
        List<Category> categories = categoryRepository.findAllActiveCategories();
        return dtoMapper.toCategoryDtoList(categories);
    }

    @Transactional(readOnly = true)
    public PaginatedResponse<ProductDto> getProducts(Long categoryId, int page, int limit, String search, String sortBy, String sortDir) {
        // Validate and sanitize pagination parameters (1-based input)
        page = Math.max(1, page); // Ensure page is at least 1
        limit = Math.max(1, Math.min(100, limit)); // Ensure limit is between 1 and 100

        log.info("Getting products with validated params - page: {}, limit: {}, categoryId: {}, search: '{}'",
                page, limit, categoryId, search);

        // Convert 1-based page to 0-based for Spring Data
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, limit, sort);

        Page<Product> productPage;

        try {
            if (search != null && !search.trim().isEmpty()) {
                productPage = productRepository.searchProducts(search.trim(), pageable);
            } else if (categoryId != null) {
                productPage = productRepository.findByCategoryIdWithCategory(categoryId, pageable);
            } else {
                productPage = productRepository.findAllActiveWithCategory(pageable);
            }
        } catch (Exception e) {
            log.error("Error fetching products: {}", e.getMessage(), e);
            // Return empty result on error
            return PaginatedResponse.<ProductDto>builder()
                    .data(new ArrayList<>())
                    .pagination(PaginatedResponse.PaginationInfo.builder()
                            .page(page) // Return 1-based page to client
                            .limit(limit)
                            .total(0L)
                            .totalPages(0)
                            .build())
                    .build();
        }

        List<ProductDto> productDtos = dtoMapper.toProductDtoList(productPage.getContent());

        return PaginatedResponse.<ProductDto>builder()
                .data(productDtos)
                .pagination(PaginatedResponse.PaginationInfo.builder()
                        .page(page) // Return 1-based page to client
                        .limit(limit)
                        .total(productPage.getTotalElements())
                        .totalPages(productPage.getTotalPages())
                        .build())
                .build();
    }

    @Transactional(readOnly = true)
    public ProductDto getProductById(Long productId) {
        Product product = productRepository.findByIdWithDetails(productId)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + productId));
        return dtoMapper.toProductDto(product);
    }

    @Transactional(readOnly = true)
    public List<ProductDto> getFeaturedProducts() {
        List<Product> products = productRepository.findByIsFeaturedTrueAndIsActiveTrue();
        return dtoMapper.toProductDtoList(products);
    }

    @Transactional(readOnly = true)
    public List<ProductDto> getPopularProducts(int limit) {
        // Validate limit parameter
        limit = Math.max(1, Math.min(50, limit)); // Between 1 and 50

        log.info("Getting popular products with limit: {}", limit);

        Pageable pageable = PageRequest.of(0, limit);
        List<Product> products = productRepository.findPopularProducts(pageable);
        return dtoMapper.toProductDtoList(products);
    }

    @Transactional(readOnly = true)
    public List<ProductDto> searchProducts(String query) {
        if (query == null || query.trim().isEmpty()) {
            return List.of();
        }

        Pageable pageable = PageRequest.of(0, 20); // Limit search results
        Page<Product> productPage = productRepository.searchProducts(query.trim(), pageable);
        return dtoMapper.toProductDtoList(productPage.getContent());
    }

    public CategoryDto getCategoryById(Long categoryId) {
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + categoryId));
        return dtoMapper.toCategoryDto(category);
    }

    // Admin methods for product management
    @Transactional
    public ProductDto createProduct(CreateProductRequest request) {
        Category category = categoryRepository.findById(request.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + request.getCategoryId()));

        Product product = Product.builder()
                .name(request.getName())
                .description(request.getDescription())
                .price(request.getPrice())
                .originalPrice(request.getOriginalPrice())
                .discount(request.getDiscount())
                .image(request.getImage())
                .category(category)
                .unit(request.getUnit())
                .inStock(request.getInStock())
                .rating(request.getRating())
                .reviewCount(request.getReviewCount())
                .stockQuantity(request.getStockQuantity() != null ? request.getStockQuantity() : 0)
                .isActive(true)
                .isFeatured(false)
                .build();

        Product savedProduct = productRepository.save(product);

        // Handle tags if provided
        if (request.getTags() != null && !request.getTags().isEmpty()) {
            final Product finalProduct = savedProduct; // Make it effectively final for lambda
            List<ProductTag> productTags = request.getTags().stream()
                    .map(tagName -> ProductTag.builder()
                            .name(tagName)
                            .product(finalProduct)
                            .build())
                    .collect(Collectors.toList());
            savedProduct.getTags().addAll(productTags);
            savedProduct = productRepository.save(savedProduct);
        }

        return dtoMapper.toProductDto(savedProduct);
    }

    @Transactional
    public ProductDto updateProduct(Long productId, CreateProductRequest request) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + productId));

        Category category = categoryRepository.findById(request.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + request.getCategoryId()));

        product.setName(request.getName());
        product.setDescription(request.getDescription());
        product.setPrice(request.getPrice());
        product.setOriginalPrice(request.getOriginalPrice());
        product.setDiscount(request.getDiscount());
        product.setImage(request.getImage());
        product.setCategory(category);
        product.setUnit(request.getUnit());
        product.setInStock(request.getInStock());
        product.setRating(request.getRating());
        product.setReviewCount(request.getReviewCount());
        product.setStockQuantity(request.getStockQuantity() != null ? request.getStockQuantity() : 0);

        // Handle tags update
        if (request.getTags() != null) {
            // Clear existing tags
            product.getTags().clear();

            // Add new tags if provided
            if (!request.getTags().isEmpty()) {
                final Product finalProduct = product; // Make it effectively final for lambda
                List<ProductTag> productTags = request.getTags().stream()
                        .map(tagName -> ProductTag.builder()
                                .name(tagName)
                                .product(finalProduct)
                                .build())
                        .collect(Collectors.toList());
                product.getTags().addAll(productTags);
            }
        }

        Product savedProduct = productRepository.save(product);
        return dtoMapper.toProductDto(savedProduct);
    }

    @Transactional
    public void deleteProduct(Long productId) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + productId));

        // Soft delete by setting isActive to false
        product.setIsActive(false);
        productRepository.save(product);
    }

    @Transactional
    public ProductDto toggleProductFeaturedStatus(Long productId) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + productId));

        product.setIsFeatured(!product.getIsFeatured());
        Product savedProduct = productRepository.save(product);
        return dtoMapper.toProductDto(savedProduct);
    }

    // Admin methods for category management
    public List<CategoryDto> getAllCategoriesForAdmin() {
        List<Category> categories = categoryRepository.findAll(Sort.by(Sort.Direction.ASC, "sortOrder"));
        return dtoMapper.toCategoryDtoList(categories);
    }

    @Transactional
    public CategoryDto createCategory(CreateCategoryRequest request) {
        if (categoryRepository.existsByName(request.getName())) {
            throw new IllegalArgumentException("Category with name '" + request.getName() + "' already exists");
        }

        Category category = Category.builder()
                .name(request.getName())
                .image(request.getImage())
                .icon(request.getIcon())
                .color(request.getColor())
                .isActive(request.getIsActive())
                .sortOrder(request.getSortOrder())
                .build();

        Category savedCategory = categoryRepository.save(category);
        return dtoMapper.toCategoryDto(savedCategory);
    }

    @Transactional
    public CategoryDto updateCategory(Long categoryId, CreateCategoryRequest request) {
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + categoryId));

        // Check if name is being changed and if it conflicts with existing category
        if (!category.getName().equals(request.getName()) && categoryRepository.existsByName(request.getName())) {
            throw new IllegalArgumentException("Category with name '" + request.getName() + "' already exists");
        }

        category.setName(request.getName());
        category.setImage(request.getImage());
        category.setIcon(request.getIcon());
        category.setColor(request.getColor());
        category.setIsActive(request.getIsActive());
        category.setSortOrder(request.getSortOrder());

        Category savedCategory = categoryRepository.save(category);
        return dtoMapper.toCategoryDto(savedCategory);
    }

    @Transactional
    public void deleteCategory(Long categoryId) {
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new ResourceNotFoundException("Category not found with id: " + categoryId));

        // Check if category has products
        if (!category.getProducts().isEmpty()) {
            throw new IllegalArgumentException("Cannot delete category with existing products");
        }

        categoryRepository.delete(category);
    }
}
