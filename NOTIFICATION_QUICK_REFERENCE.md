# 🚀 Notification System Quick Reference

A quick reference guide for common notification system tasks and troubleshooting.

## 🔧 Quick Setup Commands

### Backend Setup (5 minutes)

```bash
# 1. Configure Firebase
cd grocease-backend/src/main/resources
cp firebase-service-account.json.example firebase-service-account.json
# Edit with your Firebase credentials

# 2. Update application.yml
echo "firebase:
  enabled: true
  config:
    file: firebase-service-account.json" >> application.yml

# 3. Start backend
cd ../../../
./mvnw spring-boot:run
```

### Mobile App Setup (2 minutes)

```bash
# 1. Ensure dependencies are installed
cd grocease
npm install expo-notifications

# 2. Start app
npm start
```

### Admin Panel Setup (2 minutes)

```bash
# 1. Start admin panel
cd grocease-admin-panel
npm run dev

# 2. Request browser notification permission when prompted
```

## 📱 Testing Checklist

### ✅ Customer Notification Test

1. **Register Device Token**
   - Open mobile app
   - Login as customer
   - Check logs: "Device token registered successfully"

2. **Place Test Order**
   - Add items to cart
   - Complete checkout
   - Order should appear in admin panel

3. **Update Order Status**
   - Go to admin panel → Orders
   - Find the order → Change status to "CONFIRMED"
   - Customer should receive push notification

4. **Verify Notification**
   - Check mobile device for notification
   - Tap notification → should navigate to order details
   - Check database: `SELECT * FROM notification_history ORDER BY created_at DESC LIMIT 5;`

### ✅ Admin Notification Test

1. **Enable Browser Notifications**
   - Open admin panel
   - Allow notification permission when prompted

2. **Test New Order Alert**
   - Place order from mobile app
   - Admin panel should show real-time notification
   - Browser notification should appear

3. **Verify Real-time Updates**
   - Keep admin panel open
   - Place another order from mobile
   - Order should appear immediately in orders list

## 🔍 Common API Endpoints

### Device Token Registration
```http
POST /notifications/register-token
Authorization: Bearer {user-jwt-token}
Content-Type: application/x-www-form-urlencoded

deviceToken=ExponentPushToken[xxx]&deviceType=android&deviceId=unique-device-id
```

### Update Order Status (Triggers Notification)
```http
PUT /admin/orders/{orderId}/status
Authorization: Bearer {admin-jwt-token}
Content-Type: application/json

{
  "status": "CONFIRMED",
  "notes": "Order confirmed by admin"
}
```

### Send Custom Notification
```http
POST /admin/notifications/send
Authorization: Bearer {admin-jwt-token}
Content-Type: application/json

{
  "title": "Special Offer!",
  "message": "Get 20% off on your next order",
  "userIds": [1, 2, 3],
  "type": "PROMOTIONAL"
}
```

### Get Notification History
```http
GET /admin/notifications/history?page=0&limit=10
Authorization: Bearer {admin-jwt-token}
```

## 🐛 Quick Troubleshooting

### Issue: "Firebase messaging not available"

**Solution:**
```bash
# Check if firebase-service-account.json exists
ls -la grocease-backend/src/main/resources/firebase-service-account.json

# Verify file content (should be valid JSON)
head -5 grocease-backend/src/main/resources/firebase-service-account.json

# Check application.yml
grep -A 5 "firebase:" grocease-backend/src/main/resources/application.yml
```

### Issue: "No active device tokens found"

**Solution:**
```sql
-- Check if device tokens are registered
SELECT u.name, udt.device_token, udt.is_active, udt.created_at 
FROM user_device_tokens udt 
JOIN users u ON udt.user_id = u.id 
ORDER BY udt.created_at DESC;

-- If no tokens, ensure mobile app calls registration endpoint
-- Check mobile app logs for "Device token registered successfully"
```

### Issue: "Notifications not received on mobile"

**Checklist:**
```bash
# 1. Check device permissions
# - Go to device Settings → Apps → GrocEase → Notifications → Ensure enabled

# 2. Verify token registration
SELECT * FROM user_device_tokens WHERE user_id = YOUR_USER_ID;

# 3. Check notification history
SELECT * FROM notification_history WHERE user_id = YOUR_USER_ID ORDER BY created_at DESC LIMIT 5;

# 4. Test with Expo push tool
curl -H "Content-Type: application/json" \
     -X POST https://exp.host/--/api/v2/push/send \
     -d '{
       "to": "ExponentPushToken[YOUR_TOKEN]",
       "title": "Test",
       "body": "Test notification"
     }'
```

### Issue: "Admin panel not showing real-time updates"

**Solution:**
```javascript
// Check WebSocket connection in browser console
console.log('WebSocket state:', socket.readyState);
// 0 = CONNECTING, 1 = OPEN, 2 = CLOSING, 3 = CLOSED

// If connection failed, check:
// 1. WebSocket URL in admin panel configuration
// 2. Backend WebSocket endpoint is running
// 3. No firewall blocking WebSocket connections
```

## 📊 Useful Database Queries

### Check Notification Statistics
```sql
-- Total notifications sent today
SELECT COUNT(*) FROM notification_history 
WHERE created_at >= CURRENT_DATE;

-- Notifications by type
SELECT type, COUNT(*) as count 
FROM notification_history 
GROUP BY type 
ORDER BY count DESC;

-- Failed notifications
SELECT * FROM notification_history 
WHERE status = 'FAILED' 
ORDER BY created_at DESC 
LIMIT 10;
```

### Check Order Status Changes
```sql
-- Recent order status changes
SELECT o.order_number, osh.from_status, osh.to_status, osh.created_at
FROM order_status_history osh
JOIN orders o ON osh.order_id = o.id
ORDER BY osh.created_at DESC
LIMIT 10;

-- Orders pending notification
SELECT o.id, o.order_number, o.status, o.created_at
FROM orders o
LEFT JOIN notification_history nh ON nh.user_id = o.user_id 
  AND nh.created_at > o.updated_at
WHERE nh.id IS NULL
  AND o.status != 'PENDING';
```

### Check Active Users and Tokens
```sql
-- Users with active device tokens
SELECT u.name, u.email, COUNT(udt.id) as token_count
FROM users u
JOIN user_device_tokens udt ON u.id = udt.user_id
WHERE udt.is_active = true
GROUP BY u.id, u.name, u.email
ORDER BY token_count DESC;
```

## ⚡ Performance Tips

### Backend Optimization
```java
// Use @Async for notification sending
@Async
public void sendOrderStatusNotification(Order order, Order.OrderStatus newStatus) {
    // Implementation
}

// Batch process notifications
@Scheduled(fixedRate = 30000) // Every 30 seconds
public void processPendingNotifications() {
    // Process queued notifications
}
```

### Mobile App Optimization
```typescript
// Cache device token to avoid re-registration
const cachedToken = await AsyncStorage.getItem('expo_push_token');
if (cachedToken) {
    return cachedToken;
}

// Debounce notification handling
const debouncedHandler = useMemo(
    () => debounce(handleNotification, 1000),
    []
);
```

### Admin Panel Optimization
```typescript
// Use React Query for caching
const { data: orders } = useQuery({
    queryKey: ['orders'],
    queryFn: fetchOrders,
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
});

// Implement WebSocket reconnection
useEffect(() => {
    const connectWebSocket = () => {
        const ws = new WebSocket(WS_URL);
        ws.onclose = () => {
            setTimeout(connectWebSocket, 5000); // Reconnect after 5 seconds
        };
    };
    connectWebSocket();
}, []);
```

## 🔐 Security Considerations

### Device Token Security
```java
// Validate device tokens before storing
@PreAuthorize("hasRole('USER')")
@PostMapping("/register-token")
public ResponseEntity<?> registerToken(@RequestParam String deviceToken) {
    // Validate token format
    if (!isValidExpoPushToken(deviceToken)) {
        throw new BadRequestException("Invalid device token format");
    }
    // Process registration
}
```

### Admin Notification Security
```java
// Ensure only admins can send notifications
@PreAuthorize("hasRole('ADMIN')")
@PostMapping("/admin/notifications/send")
public ResponseEntity<?> sendNotification(@RequestBody SendNotificationRequest request) {
    // Validate request
    // Send notification
}
```

### Rate Limiting
```java
// Implement rate limiting for notifications
@RateLimiter(name = "notification", fallbackMethod = "fallbackNotification")
public void sendNotification(SendNotificationRequest request) {
    // Send notification
}
```

---

**For complete setup instructions, see [ORDER_NOTIFICATION_SETUP_GUIDE.md](./ORDER_NOTIFICATION_SETUP_GUIDE.md)**
