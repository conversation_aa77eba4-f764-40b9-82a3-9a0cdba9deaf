// Test script to verify registration flow
// Run this in the grocease directory: node test-registration-flow.js

const API_BASE_URL = 'http://localhost:8080';

async function testRegistrationFlow() {
  console.log('🧪 Testing Registration Flow...\n');

  // Test data
  const testUser = {
    name: 'Test User',
    email: '<EMAIL>',
    phone: '+1234567890',
    password: 'password123',
    confirmPassword: 'password123'
  };

  try {
    // Step 1: Test Registration
    console.log('📝 Step 1: Testing Registration...');
    const registerResponse = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });

    const registerData = await registerResponse.json();
    console.log('Registration Response:', JSON.stringify(registerData, null, 2));

    if (!registerData.success) {
      console.error('❌ Registration failed:', registerData.message);
      return;
    }

    // Check if email verification is required
    const requiresEmailVerification = registerData.data?.requiresEmailVerification;
    console.log(`\n🔍 Requires Email Verification: ${requiresEmailVerification}`);
    console.log(`🔍 Token Present: ${!!registerData.data?.token}`);
    console.log(`🔍 User Email Verified: ${registerData.data?.user?.isEmailVerified}`);

    if (requiresEmailVerification) {
      console.log('✅ Correct: Registration requires email verification');
      console.log('✅ Correct: No tokens returned');
      
      // Step 2: Check for OTP in backend logs
      console.log('\n📧 Step 2: Check backend logs for OTP...');
      console.log('Look for log message: "*** DEVELOPMENT OTP FOR TESTING: XXXXXX ***"');
      
      // For testing, let's try a common OTP
      const testOtp = '123456'; // You'll need to get this from backend logs
      console.log(`\n🔐 Step 3: Testing OTP Verification with OTP: ${testOtp}`);
      console.log('⚠️  Replace this with actual OTP from backend logs');
      
      // Uncomment and run this part after getting the real OTP from logs
      /*
      const otpResponse = await fetch(`${API_BASE_URL}/auth/verify-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testUser.email,
          otp: testOtp,
          type: 'EMAIL_VERIFICATION'
        })
      });

      const otpData = await otpResponse.json();
      console.log('OTP Verification Response:', JSON.stringify(otpData, null, 2));

      if (otpData.success && otpData.data?.token) {
        console.log('✅ OTP verification successful - tokens returned');
      } else {
        console.log('❌ OTP verification failed or no tokens returned');
      }
      */
      
    } else {
      console.log('❌ Problem: Registration should require email verification');
      console.log('❌ Problem: Tokens should not be returned immediately');
      
      if (registerData.data?.token) {
        console.log('❌ Issue: User is authenticated immediately (this is wrong)');
      }
    }

    // Step 4: Check database
    console.log('\n🗄️  Step 4: Check Database');
    console.log('Run this SQL query to verify user state:');
    console.log(`SELECT id, name, email, is_email_verified, is_active FROM users WHERE email = '${testUser.email}';`);
    console.log('Expected: is_email_verified should be FALSE');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure backend is running on http://localhost:8080');
    console.log('2. Check backend logs for errors');
    console.log('3. Verify email verification is enabled in application.yml');
  }
}

// Helper function to clean up test data
async function cleanupTestData() {
  console.log('\n🧹 Cleanup: Delete test user from database');
  console.log('Run these SQL commands:');
  console.log("DELETE FROM otp_tokens WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>');");
  console.log("DELETE FROM users WHERE email = '<EMAIL>';");
}

// Run the test
testRegistrationFlow().then(() => {
  cleanupTestData();
}).catch(console.error);
