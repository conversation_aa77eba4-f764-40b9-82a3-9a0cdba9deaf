import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useAuth } from '../../hooks/useAuth';
import Button from '../../components/Button';

type OTPVerificationRouteProp = RouteProp<{
  OTPVerification: { 
    email: string; 
    type: 'email_verification' | 'password_reset' | 'phone_verification';
  };
}, 'OTPVerification'>;

const OTPVerificationScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<OTPVerificationRouteProp>();
  const { email, type } = route.params;
  const { verifyOTP, resendOTP } = useAuth();
  
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  
  const inputRefs = useRef<(TextInput | null)[]>([]);

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer(prev => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setCanResend(true);
    }
  }, [timer]);

  const handleOtpChange = (value: string, index: number) => {
    if (value.length > 1) return; // Prevent multiple characters
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOTP = async () => {
    const otpString = otp.join('');
    
    if (otpString.length !== 6) {
      Alert.alert('Invalid OTP', 'Please enter the complete 6-digit verification code');
      return;
    }

    setIsLoading(true);
    try {
      const result = await verifyOTP({
        email,
        otp: otpString,
        type
      });

      if (result.success) {
        if (type === 'password_reset') {
          Alert.alert(
            'OTP Verified',
            'Your verification code has been confirmed. You can now reset your password.',
            [
              {
                text: 'OK',
                onPress: () => {
                  navigation.navigate('ResetPassword' as never, {
                    token: result.token
                  } as never);
                }
              }
            ]
          );
        } else if (type === 'email_verification') {
          Alert.alert(
            'Email Verified',
            'Your email has been verified successfully! Welcome to GroceEase.',
            [
              {
                text: 'OK',
                onPress: () => {
                  // Navigation will be handled automatically by auth state change
                  // User is now authenticated and will go to main app
                }
              }
            ]
          );
        }
      } else {
        Alert.alert('Verification Failed', result.message);
        // Clear OTP inputs on failure
        setOtp(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    setIsResending(true);
    try {
      const result = await resendOTP(email, type);
      
      if (result.success) {
        Alert.alert('OTP Sent', 'A new verification code has been sent to your email');
        setTimer(60);
        setCanResend(false);
        setOtp(['', '', '', '', '', '']);
        inputRefs.current[0]?.focus();
      } else {
        Alert.alert('Error', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to resend OTP. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'email_verification':
        return 'Verify Email';
      case 'password_reset':
        return 'Reset Password';
      case 'phone_verification':
        return 'Verify Phone';
      default:
        return 'Verification';
    }
  };

  const getDescription = () => {
    switch (type) {
      case 'email_verification':
        return `We've sent a verification code to ${email}. Please enter the code to verify your email address.`;
      case 'password_reset':
        return `We've sent a verification code to ${email}. Please enter the code to reset your password.`;
      case 'phone_verification':
        return `We've sent a verification code to your phone. Please enter the code to verify your phone number.`;
      default:
        return `We've sent a verification code to ${email}. Please enter the code below.`;
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="px-6 py-8">
            {/* Back Button */}
            <TouchableOpacity 
              className="w-10 h-10 rounded-full bg-neutral-100 items-center justify-center mb-8"
              onPress={() => navigation.goBack()}
            >
              <Ionicons name="arrow-back" size={20} color="#64748b" />
            </TouchableOpacity>

            {/* Header */}
            <View className="items-center mb-8">
              <View className="w-20 h-20 bg-primary-500 rounded-2xl items-center justify-center mb-4">
                <Ionicons name="mail" size={40} color="#ffffff" />
              </View>
              <Text className="text-3xl font-bold text-neutral-800 mb-2">
                {getTitle()}
              </Text>
              <Text className="text-base text-neutral-600 text-center">
                {getDescription()}
              </Text>
            </View>

            {/* OTP Input */}
            <View className="mb-8">
              <Text className="text-base font-semibold text-neutral-800 mb-4 text-center">
                Enter Verification Code
              </Text>
              <View className="flex-row justify-center space-x-3">
                {otp.map((digit, index) => (
                  <TextInput
                    key={index}
                    ref={(ref) => (inputRefs.current[index] = ref)}
                    className="w-12 h-12 bg-neutral-100 rounded-lg text-center text-lg font-bold text-neutral-800 border-2 border-transparent focus:border-primary-500"
                    value={digit}
                    onChangeText={(value) => handleOtpChange(value, index)}
                    onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                    keyboardType="numeric"
                    maxLength={1}
                    selectTextOnFocus
                  />
                ))}
              </View>
            </View>

            {/* Verify Button */}
            <View className="mb-6">
              <Button
                title="Verify Code"
                onPress={handleVerifyOTP}
                loading={isLoading}
                size="lg"
                fullWidth
              />
            </View>

            {/* Resend OTP */}
            <View className="items-center mb-6">
              {canResend ? (
                <TouchableOpacity onPress={handleResendOTP} disabled={isResending}>
                  <Text className="text-primary-600 font-semibold">
                    {isResending ? 'Sending...' : 'Resend Code'}
                  </Text>
                </TouchableOpacity>
              ) : (
                <Text className="text-neutral-500">
                  Resend code in {timer}s
                </Text>
              )}
            </View>

            {/* Demo Info */}
            <View className="bg-blue-50 rounded-2xl p-4 border border-blue-200">
              <View className="flex-row items-center mb-2">
                <Ionicons name="information-circle" size={20} color="#3b82f6" />
                <Text className="text-sm font-semibold text-blue-800 ml-2">
                  Demo Mode
                </Text>
              </View>
              <Text className="text-sm text-blue-700">
                Check the console for the OTP code. In a real app, this would be sent via email or SMS.
              </Text>
            </View>

            {/* Change Email */}
            <View className="flex-row justify-center items-center mt-8">
              <Text className="text-neutral-600">
                Didn't receive the code? 
              </Text>
              <TouchableOpacity onPress={() => navigation.goBack()} className="ml-1">
                <Text className="text-primary-600 font-semibold">
                  Change Email
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default OTPVerificationScreen;
