// API Response Types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Auth Types
export interface LoginRequest {
  email: string
  password: string
}

export interface AuthResponse {
  user: User
  token: string
  refreshToken: string
  expiresIn: number
}

// User Types
export interface User {
  id: string
  name: string
  email: string
  phone?: string
  avatar?: string
  role: string
  isEmailVerified: boolean
  isPhoneVerified: boolean
  isActive: boolean
  createdAt: string
  addresses?: Address[]
}

// Request Types
export interface CreateProductRequest {
  name: string
  description?: string
  price: number
  originalPrice?: number
  discount?: number
  image?: string
  categoryId: number
  unit: string
  inStock: boolean
  rating?: number
  reviewCount?: number
  tags?: string[]
  isFeatured?: boolean
  stockQuantity?: number
}

export interface CreateCategoryRequest {
  name: string
  image?: string
  icon?: string
  color?: string
  isActive: boolean
  sortOrder?: number
}

export interface CreateBannerRequest {
  title: string
  subtitle?: string
  image: string
  backgroundColor?: string
  textColor?: string
  actionText?: string
  actionUrl?: string
  isActive: boolean
  sortOrder?: number
}

export interface Address {
  id: string
  type: 'HOME' | 'WORK' | 'OTHER'
  street: string
  city: string
  state: string
  zipCode: string
  isDefault: boolean
}

// Product Types
export interface Product {
  id: string
  name: string
  description: string
  price: number
  originalPrice?: number
  discount?: number
  image: string
  category: string
  unit: string
  inStock: boolean
  rating: number
  reviewCount: number
  tags: string[]
  isFeatured?: boolean
  stockQuantity?: number
}

export interface Category {
  id: string
  name: string
  image?: string
  icon?: string
  color?: string
  isActive?: boolean
  sortOrder?: number
}

export interface Banner {
  id: string
  title: string
  subtitle?: string
  image: string
  backgroundColor?: string
  textColor?: string
  actionText?: string
  actionUrl?: string
  isActive: boolean
  sortOrder: number
  createdAt: string
}

// Order Types
export interface Order {
  id: string
  orderNumber: string
  total: number
  subtotal: number
  deliveryFee: number
  discount: number
  status: OrderStatus
  user?: User
  deliveryAddress: Address
  orderDate: string
  estimatedDelivery?: string
  deliveredAt?: string
  items: OrderItem[]
}

export interface OrderItem {
  product: Product
  quantity: number
  unitPrice: number
  totalPrice: number
}

export type OrderStatus = 
  | 'PENDING' 
  | 'CONFIRMED' 
  | 'PREPARING' 
  | 'OUT_FOR_DELIVERY' 
  | 'DELIVERED' 
  | 'CANCELLED'

// Analytics Types
export interface SalesData {
  date: string
  period: string
  totalRevenue: number
  orderCount: number
  averageOrderValue: number
  totalItemsSold: number
}

export interface ProductSales {
  productId: string
  productName: string
  productImage: string
  categoryName: string
  quantitySold: number
  totalRevenue: number
  orderCount: number
  averagePrice: number
}

export interface CategorySales {
  categoryId: string
  categoryName: string
  categoryImage: string
  totalRevenue: number
  orderCount: number
  totalItemsSold: number
  uniqueProductsSold: number
  averageOrderValue: number
}

export interface UserEngagement {
  date: string
  period: string
  newUsers: number
  activeUsers: number
  totalUsers: number
  retentionRate: number
  returningUsers: number
}

export interface DashboardOverview {
  todayRevenue: number
  monthlyRevenue: number
  todayOrders: number
  monthlyOrders: number
  totalUsers: number
  activeUsers: number
  averageOrderValue: number
  orderStatusDistribution: Record<string, number>
  revenueGrowthPercentage: number
  orderGrowthPercentage: number
}

// Notification Types
export interface NotificationHistory {
  id: string
  title: string
  message: string
  type: NotificationType
  status: NotificationStatus
  isBroadcast: boolean
  sentAt: string
  errorMessage?: string
}

export type NotificationType =
  | 'ORDER_CONFIRMATION'
  | 'ORDER_PREPARING'
  | 'ORDER_OUT_FOR_DELIVERY'
  | 'ORDER_DELIVERED'
  | 'ORDER_CANCELLED'
  | 'NEW_ORDER_ADMIN'
  | 'BIRTHDAY_WISH'
  | 'PROMOTIONAL_OFFER'
  | 'NEW_PRODUCT'
  | 'LOW_STOCK_ALERT'
  | 'CART_REMINDER'
  | 'GENERAL'

export type NotificationStatus = 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED'

export interface SendNotificationRequest {
  title: string
  message: string
  type: NotificationType
  userIds?: string[]
  data?: Record<string, string>
  isBroadcast?: boolean
}

// Banner Types
export interface Banner {
  id: string
  title: string
  subtitle?: string
  image: string
  backgroundColor: string
  textColor: string
  actionText?: string
  actionUrl?: string
}

// Table Types
export interface TableColumn<T> {
  key: keyof T
  label: string
  sortable?: boolean
  render?: (value: any, item: T) => React.ReactNode
}

export interface TableProps<T> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    onPageChange: (page: number) => void
  }
}

// Discount Code Types
export interface DiscountCode {
  id: string
  code: string
  name: string
  description?: string
  type: DiscountType
  value: number
  minimumOrderAmount?: number
  maximumDiscountAmount?: number
  usageLimit?: number
  usageLimitPerUser?: number
  usedCount: number
  validFrom: string
  validUntil: string
  isActive: boolean
  isFirstOrderOnly: boolean
  applicableCategories?: string[]
  applicableProducts?: string[]
  createdAt: string
  isValid?: boolean
  isExpired?: boolean
}

export type DiscountType = 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_DELIVERY'

export interface CreateDiscountCodeRequest {
  code: string
  name: string
  description?: string
  type: DiscountType
  value: number
  minimumOrderAmount?: number
  maximumDiscountAmount?: number
  usageLimit?: number
  usageLimitPerUser?: number
  validFrom: string
  validUntil: string
  isActive: boolean
  isFirstOrderOnly?: boolean
  applicableCategories?: number[]
  applicableProducts?: number[]
}

export interface ApplyDiscountRequest {
  code: string
  orderSubtotal: number
  deliveryFee: number
  items?: CartItemForDiscount[]
}

export interface CartItemForDiscount {
  productId: number
  categoryId: number
  quantity: number
  unitPrice: number
}

export interface DiscountResult {
  discountAmount: number
  finalTotal: number
  discountCode: DiscountCode
}
