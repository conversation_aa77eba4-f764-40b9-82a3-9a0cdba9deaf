import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';

import { useAuth } from '../../hooks/useAuth';
import { validateEmail, validatePhone } from '../../utils';
import Button from '../../components/Button';

const RegisterScreen = () => {
  const navigation = useNavigation();
  const { register } = useAuth();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRegister = async () => {
    // Validation
    if (!formData.name.trim()) {
      Alert.alert('Validation Error', 'Please enter your full name');
      return;
    }

    if (!formData.email.trim()) {
      Alert.alert('Validation Error', 'Please enter your email address');
      return;
    }

    if (!validateEmail(formData.email)) {
      Alert.alert('Validation Error', 'Please enter a valid email address');
      return;
    }

    if (!formData.phone.trim()) {
      Alert.alert('Validation Error', 'Please enter your phone number');
      return;
    }

    if (!validatePhone(formData.phone)) {
      Alert.alert('Validation Error', 'Please enter a valid phone number');
      return;
    }

    if (!formData.password.trim()) {
      Alert.alert('Validation Error', 'Please enter a password');
      return;
    }

    if (formData.password.length < 6) {
      Alert.alert('Validation Error', 'Password must be at least 6 characters');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Validation Error', 'Passwords do not match');
      return;
    }

    if (!acceptTerms) {
      Alert.alert('Validation Error', 'Please accept the Terms of Service and Privacy Policy');
      return;
    }

    setIsLoading(true);
    try {
      const result = await register({
        name: formData.name.trim(),
        email: formData.email.trim().toLowerCase(),
        phone: formData.phone.trim(),
        password: formData.password,
        confirmPassword: formData.confirmPassword,
      });

      if (result.success) {
        // Check if email verification is required
        const needsEmailVerification = result.data?.requiresEmailVerification;

        if (needsEmailVerification) {
          Alert.alert(
            'Registration Successful',
            'Your account has been created successfully. Please check your email and verify your account to continue.',
            [
              {
                text: 'OK',
                onPress: () => {
                  navigation.navigate('OTPVerification' as never, {
                    email: formData.email.trim().toLowerCase(),
                    type: 'email_verification'
                  } as never);
                }
              }
            ]
          );
        } else {
          Alert.alert(
            'Registration Successful',
            'Your account has been created successfully! Welcome to GroceEase.',
            [
              {
                text: 'OK',
                onPress: () => {
                  // Navigation will be handled automatically by auth state change
                  // User will go directly to main app since email verification is disabled
                }
              }
            ]
          );
        }
      } else {
        Alert.alert('Registration Failed', result.message);
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignIn = () => {
    navigation.navigate('Login' as never);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView 
        className="flex-1" 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="px-6 py-8">
            {/* Header */}
            <View className="items-center mb-8">
              <View className="w-20 h-20 bg-primary-500 rounded-2xl items-center justify-center mb-4">
                <Ionicons name="person-add" size={40} color="#ffffff" />
              </View>
              <Text className="text-3xl font-bold text-neutral-800 mb-2">
                Create Account
              </Text>
              <Text className="text-base text-neutral-600 text-center">
                Join GroceEase and start shopping for fresh groceries
              </Text>
            </View>

            {/* Registration Form */}
            <View className="space-y-4">
              {/* Full Name Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Full Name
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Enter your full name"
                    placeholderTextColor="#94a3b8"
                    value={formData.name}
                    onChangeText={(value) => handleInputChange('name', value)}
                    autoCapitalize="words"
                    autoCorrect={false}
                  />
                  <View className="absolute right-4 top-3">
                    <Ionicons name="person" size={20} color="#94a3b8" />
                  </View>
                </View>
              </View>

              {/* Email Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Email Address
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Enter your email"
                    placeholderTextColor="#94a3b8"
                    value={formData.email}
                    onChangeText={(value) => handleInputChange('email', value)}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <View className="absolute right-4 top-3">
                    <Ionicons name="mail" size={20} color="#94a3b8" />
                  </View>
                </View>
              </View>

              {/* Phone Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Phone Number
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Enter your phone number"
                    placeholderTextColor="#94a3b8"
                    value={formData.phone}
                    onChangeText={(value) => handleInputChange('phone', value)}
                    keyboardType="phone-pad"
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <View className="absolute right-4 top-3">
                    <Ionicons name="call" size={20} color="#94a3b8" />
                  </View>
                </View>
              </View>

              {/* Password Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Password
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Create a password"
                    placeholderTextColor="#94a3b8"
                    value={formData.password}
                    onChangeText={(value) => handleInputChange('password', value)}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    className="absolute right-4 top-3"
                    onPress={() => setShowPassword(!showPassword)}
                  >
                    <Ionicons 
                      name={showPassword ? "eye-off" : "eye"} 
                      size={20} 
                      color="#94a3b8" 
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Confirm Password Input */}
              <View>
                <Text className="text-base font-semibold text-neutral-800 mb-2">
                  Confirm Password
                </Text>
                <View className="relative">
                  <TextInput
                    className="bg-neutral-100 rounded-lg px-4 py-3 pr-12 text-base text-neutral-800"
                    placeholder="Confirm your password"
                    placeholderTextColor="#94a3b8"
                    value={formData.confirmPassword}
                    onChangeText={(value) => handleInputChange('confirmPassword', value)}
                    secureTextEntry={!showConfirmPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                  />
                  <TouchableOpacity
                    className="absolute right-4 top-3"
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Ionicons 
                      name={showConfirmPassword ? "eye-off" : "eye"} 
                      size={20} 
                      color="#94a3b8" 
                    />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Terms and Conditions */}
              <View className="flex-row items-start mt-4">
                <TouchableOpacity
                  className={`w-5 h-5 rounded border-2 mr-3 mt-1 items-center justify-center ${
                    acceptTerms ? 'bg-primary-500 border-primary-500' : 'border-neutral-300'
                  }`}
                  onPress={() => setAcceptTerms(!acceptTerms)}
                >
                  {acceptTerms && (
                    <Ionicons name="checkmark" size={12} color="#ffffff" />
                  )}
                </TouchableOpacity>
                <View className="flex-1">
                  <Text className="text-sm text-neutral-600 leading-5">
                    I agree to the{' '}
                    <Text className="text-primary-600 font-semibold">Terms of Service</Text>
                    {' '}and{' '}
                    <Text className="text-primary-600 font-semibold">Privacy Policy</Text>
                  </Text>
                </View>
              </View>

              {/* Register Button */}
              <View className="mt-6">
                <Button
                  title="Create Account"
                  onPress={handleRegister}
                  loading={isLoading}
                  size="lg"
                  fullWidth
                />
              </View>

              {/* Sign In Link */}
              <View className="flex-row justify-center items-center mt-6">
                <Text className="text-neutral-600">
                  Already have an account? 
                </Text>
                <TouchableOpacity onPress={handleSignIn} className="ml-1">
                  <Text className="text-primary-600 font-semibold">
                    Sign In
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default RegisterScreen;
