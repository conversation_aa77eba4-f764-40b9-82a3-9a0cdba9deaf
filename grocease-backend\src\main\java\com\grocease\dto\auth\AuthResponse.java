package com.grocease.dto.auth;

import com.grocease.dto.user.UserDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthResponse {
    private UserDto user;
    private String token;
    private String refreshToken;
    private long expiresIn;
    private boolean requiresEmailVerification;
}
