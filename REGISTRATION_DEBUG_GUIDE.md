# 🐛 Registration Flow Debug Guide

This guide will help you debug and fix the registration flow issue where users are going directly to home screen instead of OTP verification.

## 🔧 Step 1: Start Backend with Debug Logs

1. **Open Terminal** and navigate to backend:
   ```bash
   cd grocease-backend
   ```

2. **Start the backend** (use appropriate command for your system):
   ```bash
   # Windows
   mvn spring-boot:run
   
   # Or if you have the wrapper
   ./mvnw spring-boot:run
   
   # Or using the batch file
   run.bat
   ```

3. **Watch for these log messages** during startup:
   ```
   Email verification enabled: true
   ```

## 🔧 Step 2: Test Registration API Directly

Before testing the mobile app, let's test the backend API directly:

### Test Registration Endpoint

```bash
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "password": "password123",
    "confirmPassword": "password123"
  }'
```

### Expected Response (Email Verification Enabled):
```json
{
  "data": {
    "user": {
      "id": "1",
      "name": "Test User",
      "email": "<EMAIL>",
      "isEmailVerified": false
    },
    "token": null,
    "refreshToken": null,
    "expiresIn": 0,
    "requiresEmailVerification": true
  },
  "success": true,
  "message": "User registered successfully"
}
```

### Check Backend Logs:
You should see these logs:
```
Email verification enabled: true
User saved to database: <EMAIL> with isEmailVerified: false
Generating and sending OTP for email verification...
Generated OTP: 123456 for user: <EMAIL> (type: EMAIL_VERIFICATION)
OTP token saved to database for user: <EMAIL>
Attempting to send email with subject: Verify Your Email - GrocEase to: <EMAIL>
*** DEVELOPMENT OTP FOR TESTING: 123456 ***
Returning registration response with requiresEmailVerification: true
```

## 🔧 Step 3: Test Mobile App Registration

1. **Start the mobile app**:
   ```bash
   cd grocease
   npm start
   ```

2. **Open the app** and go to Register screen

3. **Fill in the registration form** with test data

4. **Check the console logs** in your development environment for:
   ```
   Calling register API with data: {...}
   Register API response: {...}
   Registration successful, checking email verification requirement
   Response data: {...}
   Needs email verification: true
   Navigating to OTP verification screen
   ```

## 🐛 Debugging Issues

### Issue 1: "Email verification enabled: false" in logs

**Problem**: Email verification is disabled
**Solution**: 
1. Check `grocease-backend/src/main/resources/application.yml`
2. Ensure it has:
   ```yaml
   app:
     features:
       email-verification:
         enabled: true
   ```

### Issue 2: Registration API returns tokens immediately

**Problem**: Backend is not respecting email verification setting
**Solution**: 
1. Check the registration API response
2. If `requiresEmailVerification` is `false` or missing, check:
   - `AppProperties.java` configuration
   - `AuthService.register()` method
   - Application.yml settings

### Issue 3: Mobile app goes to home screen despite `requiresEmailVerification: true`

**Problem**: Mobile app is not handling the response correctly
**Solution**:
1. Check console logs for:
   ```
   Needs email verification: true
   ```
2. If this shows `false`, check the API response structure
3. If this shows `true` but still goes to home screen, check:
   - `useAuth.register()` method
   - `RegisterScreen.tsx` navigation logic

### Issue 4: No OTP in backend logs

**Problem**: OTP generation or email service issue
**Solution**:
1. Check for these logs:
   ```
   Generated OTP: 123456 for user: <EMAIL>
   *** DEVELOPMENT OTP FOR TESTING: 123456 ***
   ```
2. If missing, check:
   - `AuthService.generateAndSendOtp()` method
   - Database connection for OTP token saving

## 🔧 Step 4: Test OTP Verification

Once you get the OTP from backend logs:

### Test OTP API Directly:
```bash
curl -X POST http://localhost:8080/auth/verify-otp \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp": "123456",
    "type": "EMAIL_VERIFICATION"
  }'
```

### Expected Response:
```json
{
  "data": {
    "user": {
      "id": "1",
      "name": "Test User",
      "email": "<EMAIL>",
      "isEmailVerified": true
    },
    "token": "jwt-token-here",
    "refreshToken": "refresh-token-here",
    "expiresIn": 86400000,
    "requiresEmailVerification": false
  },
  "success": true,
  "message": "Email verified successfully. You are now logged in."
}
```

## 🔧 Step 5: Database Verification

Check the database to ensure everything is working:

```sql
-- Check user was created
SELECT id, name, email, is_email_verified, is_active FROM users WHERE email = '<EMAIL>';

-- Check OTP was generated
SELECT token, type, is_used, expires_at FROM otp_tokens WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>');

-- After OTP verification, check user is verified
SELECT name, email, is_email_verified FROM users WHERE email = '<EMAIL>';
```

## 🎯 Quick Fix Checklist

If the issue persists, check these files in order:

1. **Backend Configuration**:
   - [ ] `application.yml` has `email-verification.enabled: true`
   - [ ] `AppProperties.java` is reading the configuration correctly

2. **Backend Logic**:
   - [ ] `AuthService.register()` checks email verification setting
   - [ ] `AuthService.register()` returns `requiresEmailVerification: true`
   - [ ] `AuthService.generateAndSendOtp()` is called and generates OTP

3. **Mobile App Logic**:
   - [ ] `useAuth.register()` checks `response.data.requiresEmailVerification`
   - [ ] `RegisterScreen.tsx` navigates to OTP screen when needed
   - [ ] Console logs show correct flow

4. **API Response Structure**:
   - [ ] Registration API returns correct structure
   - [ ] Mobile app types match backend response
   - [ ] No authentication happens during registration

## 🚀 Expected Working Flow

1. **Registration** → Backend saves user with `isEmailVerified: false`
2. **OTP Generation** → Backend generates and logs OTP
3. **API Response** → Returns `requiresEmailVerification: true`, no tokens
4. **Mobile Navigation** → Goes to OTP verification screen
5. **OTP Verification** → Backend returns tokens, user authenticated
6. **Final Navigation** → User goes to home screen

---

**Follow this guide step by step to identify where the flow is breaking and fix the issue.**
