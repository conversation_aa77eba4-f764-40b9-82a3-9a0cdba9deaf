'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import AdminLayout from '@/components/layout/AdminLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import {
  Search,
  Plus,
  Edit,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Percent,
  DollarSign,
  Truck,
  Calendar,
  Users,
  ToggleLeft,
  ToggleRight
} from 'lucide-react'
import apiClient from '@/lib/api'
import { DiscountCode, CreateDiscountCodeRequest, DiscountType } from '@/types'
import { formatCurrency, formatNumber } from '@/lib/utils'
import { toast } from 'sonner'

export default function DiscountCodesPage() {
  const [page, setPage] = useState(0)
  const [search, setSearch] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingDiscountCode, setEditingDiscountCode] = useState<DiscountCode | null>(null)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [discountCodeToDelete, setDiscountCodeToDelete] = useState<string | null>(null)
  const [formData, setFormData] = useState<CreateDiscountCodeRequest>({
    code: '',
    name: '',
    description: '',
    type: 'PERCENTAGE',
    value: 0,
    minimumOrderAmount: 0,
    maximumDiscountAmount: 0,
    usageLimit: undefined,
    usageLimitPerUser: undefined,
    validFrom: '',
    validUntil: '',
    isActive: true,
    isFirstOrderOnly: false,
    applicableCategories: [],
    applicableProducts: [],
  })

  const queryClient = useQueryClient()

  const { data: discountCodesData, isLoading } = useQuery({
    queryKey: ['discount-codes', page, search],
    queryFn: () => apiClient.getDiscountCodes(page, 10, search || undefined),
  })

  const createMutation = useMutation({
    mutationFn: (data: CreateDiscountCodeRequest) => apiClient.createDiscountCode(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['discount-codes'] })
      setIsCreateDialogOpen(false)
      resetForm()
      toast.success('Discount code created successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create discount code')
    },
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: CreateDiscountCodeRequest }) =>
      apiClient.updateDiscountCode(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['discount-codes'] })
      setIsEditDialogOpen(false)
      setEditingDiscountCode(null)
      resetForm()
      toast.success('Discount code updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update discount code')
    },
  })

  const deleteMutation = useMutation({
    mutationFn: (id: string) => apiClient.deleteDiscountCode(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['discount-codes'] })
      toast.success('Discount code deleted successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete discount code')
    },
  })

  const toggleStatusMutation = useMutation({
    mutationFn: (id: string) => apiClient.toggleDiscountCodeStatus(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['discount-codes'] })
      toast.success('Discount code status updated successfully')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update discount code status')
    },
  })

  const resetForm = () => {
    setFormData({
      code: '',
      name: '',
      description: '',
      type: 'PERCENTAGE',
      value: 0,
      minimumOrderAmount: 0,
      maximumDiscountAmount: 0,
      usageLimit: undefined,
      usageLimitPerUser: undefined,
      validFrom: '',
      validUntil: '',
      isActive: true,
      isFirstOrderOnly: false,
      applicableCategories: [],
      applicableProducts: [],
    })
  }

  const handleCreate = () => {
    createMutation.mutate(formData)
  }

  const handleEdit = (discountCode: DiscountCode) => {
    setEditingDiscountCode(discountCode)
    setFormData({
      code: discountCode.code,
      name: discountCode.name,
      description: discountCode.description || '',
      type: discountCode.type,
      value: discountCode.value,
      minimumOrderAmount: discountCode.minimumOrderAmount || 0,
      maximumDiscountAmount: discountCode.maximumDiscountAmount || 0,
      usageLimit: discountCode.usageLimit,
      usageLimitPerUser: discountCode.usageLimitPerUser,
      validFrom: discountCode.validFrom.split('T')[0] + 'T' + discountCode.validFrom.split('T')[1].substring(0, 5),
      validUntil: discountCode.validUntil.split('T')[0] + 'T' + discountCode.validUntil.split('T')[1].substring(0, 5),
      isActive: discountCode.isActive,
      isFirstOrderOnly: discountCode.isFirstOrderOnly,
      applicableCategories: discountCode.applicableCategories?.map(id => parseInt(id)) || [],
      applicableProducts: discountCode.applicableProducts?.map(id => parseInt(id)) || [],
    })
    setIsEditDialogOpen(true)
  }

  const handleUpdate = () => {
    if (editingDiscountCode) {
      updateMutation.mutate({ id: editingDiscountCode.id, data: formData })
    }
  }

  const handleDelete = (id: string) => {
    setDiscountCodeToDelete(id)
    setShowDeleteDialog(true)
  }

  const confirmDelete = () => {
    if (discountCodeToDelete) {
      deleteMutation.mutate(discountCodeToDelete)
      setShowDeleteDialog(false)
      setDiscountCodeToDelete(null)
    }
  }

  const handleToggleStatus = (id: string) => {
    toggleStatusMutation.mutate(id)
  }

  const getDiscountTypeIcon = (type: DiscountType) => {
    switch (type) {
      case 'PERCENTAGE':
        return <Percent className="h-4 w-4" />
      case 'FIXED_AMOUNT':
        return <DollarSign className="h-4 w-4" />
      case 'FREE_DELIVERY':
        return <Truck className="h-4 w-4" />
      default:
        return <Percent className="h-4 w-4" />
    }
  }

  const formatDiscountValue = (type: DiscountType, value: number) => {
    switch (type) {
      case 'PERCENTAGE':
        return `${Math.round(value)}%`
      case 'FIXED_AMOUNT':
        return formatCurrency(value)
      case 'FREE_DELIVERY':
        return 'Free Delivery'
      default:
        return Math.round(value).toString()
    }
  }

  const filteredDiscountCodes = discountCodesData?.data || []

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Discount Codes</h1>
            <p className="text-muted-foreground">
              Manage promotional discount codes and coupons
            </p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Add Discount Code
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>

        {/* Search */}
        <Card>
          <CardHeader>
            <CardTitle>Search Discount Codes</CardTitle>
            <CardDescription>Find discount codes by name or code</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search discount codes..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Discount Codes Table */}
        <Card>
          <CardHeader>
            <CardTitle>Discount Codes List</CardTitle>
            <CardDescription>
              {discountCodesData?.pagination.total || 0} total discount codes
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Code</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Value</TableHead>
                      <TableHead>Usage</TableHead>
                      <TableHead>Valid Until</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDiscountCodes.map((discountCode) => (
                      <TableRow key={discountCode.id}>
                        <TableCell>
                          <div className="font-mono font-medium">
                            {discountCode.code}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{discountCode.name}</p>
                            {discountCode.description && (
                              <p className="text-sm text-muted-foreground">
                                {discountCode.description}
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getDiscountTypeIcon(discountCode.type)}
                            <span className="capitalize">
                              {discountCode.type.replace('_', ' ').toLowerCase()}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium">
                            {formatDiscountValue(discountCode.type, discountCode.value)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <p>{discountCode.usedCount} used</p>
                            {discountCode.usageLimit && (
                              <p className="text-muted-foreground">
                                of {discountCode.usageLimit} limit
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <p>{new Date(discountCode.validUntil).toLocaleDateString()}</p>
                            <p className={`text-xs ${discountCode.isExpired ? 'text-red-600' : 'text-muted-foreground'}`}>
                              {discountCode.isExpired ? 'Expired' : 'Active'}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={discountCode.isActive ? "default" : "destructive"}
                          >
                            {discountCode.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(discountCode)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleToggleStatus(discountCode.id)}
                              disabled={toggleStatusMutation.isPending}
                            >
                              {discountCode.isActive ? (
                                <ToggleRight className="h-4 w-4" />
                              ) : (
                                <ToggleLeft className="h-4 w-4" />
                              )}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(discountCode.id)}
                              disabled={deleteMutation.isPending}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {discountCodesData?.pagination && (
                  <div className="flex items-center justify-between mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing {page * 10 + 1} to {Math.min((page + 1) * 10, discountCodesData.pagination.total)} of{' '}
                      {discountCodesData.pagination.total} discount codes
                    </p>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPage(page - 1)}
                        disabled={page === 0}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPage(page + 1)}
                        disabled={page >= discountCodesData.pagination.totalPages - 1}
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* Create Discount Code Dialog */}
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Create Discount Code</DialogTitle>
              <DialogDescription>
                Add a new discount code for promotional offers.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="code">Discount Code</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                    placeholder="SAVE20"
                    className="font-mono"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="20% Off Sale"
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Special discount for new customers"
                  rows={2}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="type">Discount Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: DiscountType) => setFormData({ ...formData, type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select discount type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                      <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                      <SelectItem value="FREE_DELIVERY">Free Delivery</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="value">
                    Value {formData.type === 'PERCENTAGE' ? '(%)' : formData.type === 'FIXED_AMOUNT' ? '(Rs)' : ''}
                  </Label>
                  <Input
                    id="value"
                    type="number"
                    step="1"
                    min="0"
                    max={formData.type === 'PERCENTAGE' ? '100' : undefined}
                    value={formData.value}
                    onChange={(e) => setFormData({ ...formData, value: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                    disabled={formData.type === 'FREE_DELIVERY'}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="minimumOrderAmount">Minimum Order Amount (Rs)</Label>
                  <Input
                    id="minimumOrderAmount"
                    type="number"
                    step="1"
                    min="0"
                    value={formData.minimumOrderAmount || ''}
                    onChange={(e) => setFormData({ ...formData, minimumOrderAmount: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="maximumDiscountAmount">Maximum Discount Amount (Rs)</Label>
                  <Input
                    id="maximumDiscountAmount"
                    type="number"
                    step="1"
                    min="0"
                    value={formData.maximumDiscountAmount || ''}
                    onChange={(e) => setFormData({ ...formData, maximumDiscountAmount: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="usageLimit">Usage Limit (Total)</Label>
                  <Input
                    id="usageLimit"
                    type="number"
                    min="1"
                    value={formData.usageLimit || ''}
                    onChange={(e) => setFormData({ ...formData, usageLimit: parseInt(e.target.value) || undefined })}
                    placeholder="Unlimited"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="usageLimitPerUser">Usage Limit Per User</Label>
                  <Input
                    id="usageLimitPerUser"
                    type="number"
                    min="1"
                    value={formData.usageLimitPerUser || ''}
                    onChange={(e) => setFormData({ ...formData, usageLimitPerUser: parseInt(e.target.value) || undefined })}
                    placeholder="Unlimited"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="validFrom">Valid From</Label>
                  <Input
                    id="validFrom"
                    type="datetime-local"
                    value={formData.validFrom}
                    onChange={(e) => setFormData({ ...formData, validFrom: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="validUntil">Valid Until</Label>
                  <Input
                    id="validUntil"
                    type="datetime-local"
                    value={formData.validUntil}
                    onChange={(e) => setFormData({ ...formData, validUntil: e.target.value })}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isFirstOrderOnly"
                    checked={formData.isFirstOrderOnly}
                    onCheckedChange={(checked) => setFormData({ ...formData, isFirstOrderOnly: checked })}
                  />
                  <Label htmlFor="isFirstOrderOnly">First Order Only</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={handleCreate}
                disabled={createMutation.isPending || !formData.code.trim() || !formData.name.trim() || !formData.validFrom || !formData.validUntil}
              >
                {createMutation.isPending ? 'Creating...' : 'Create Discount Code'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Discount Code Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Edit Discount Code</DialogTitle>
              <DialogDescription>
                Update the discount code information.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-code">Discount Code</Label>
                  <Input
                    id="edit-code"
                    value={formData.code}
                    onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                    placeholder="SAVE20"
                    className="font-mono"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-name">Name</Label>
                  <Input
                    id="edit-name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="20% Off Sale"
                  />
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Special discount for new customers"
                  rows={2}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-type">Discount Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value: DiscountType) => setFormData({ ...formData, type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select discount type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                      <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                      <SelectItem value="FREE_DELIVERY">Free Delivery</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-value">
                    Value {formData.type === 'PERCENTAGE' ? '(%)' : formData.type === 'FIXED_AMOUNT' ? '(Rs)' : ''}
                  </Label>
                  <Input
                    id="edit-value"
                    type="number"
                    step="1"
                    min="0"
                    max={formData.type === 'PERCENTAGE' ? '100' : undefined}
                    value={formData.value}
                    onChange={(e) => setFormData({ ...formData, value: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                    disabled={formData.type === 'FREE_DELIVERY'}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-minimumOrderAmount">Minimum Order Amount (Rs)</Label>
                  <Input
                    id="edit-minimumOrderAmount"
                    type="number"
                    step="1"
                    min="0"
                    value={formData.minimumOrderAmount || ''}
                    onChange={(e) => setFormData({ ...formData, minimumOrderAmount: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-maximumDiscountAmount">Maximum Discount Amount (Rs)</Label>
                  <Input
                    id="edit-maximumDiscountAmount"
                    type="number"
                    step="1"
                    min="0"
                    value={formData.maximumDiscountAmount || ''}
                    onChange={(e) => setFormData({ ...formData, maximumDiscountAmount: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-usageLimit">Usage Limit (Total)</Label>
                  <Input
                    id="edit-usageLimit"
                    type="number"
                    min="1"
                    value={formData.usageLimit || ''}
                    onChange={(e) => setFormData({ ...formData, usageLimit: parseInt(e.target.value) || undefined })}
                    placeholder="Unlimited"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-usageLimitPerUser">Usage Limit Per User</Label>
                  <Input
                    id="edit-usageLimitPerUser"
                    type="number"
                    min="1"
                    value={formData.usageLimitPerUser || ''}
                    onChange={(e) => setFormData({ ...formData, usageLimitPerUser: parseInt(e.target.value) || undefined })}
                    placeholder="Unlimited"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-validFrom">Valid From</Label>
                  <Input
                    id="edit-validFrom"
                    type="datetime-local"
                    value={formData.validFrom}
                    onChange={(e) => setFormData({ ...formData, validFrom: e.target.value })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-validUntil">Valid Until</Label>
                  <Input
                    id="edit-validUntil"
                    type="datetime-local"
                    value={formData.validUntil}
                    onChange={(e) => setFormData({ ...formData, validUntil: e.target.value })}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  />
                  <Label htmlFor="edit-isActive">Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-isFirstOrderOnly"
                    checked={formData.isFirstOrderOnly}
                    onCheckedChange={(checked) => setFormData({ ...formData, isFirstOrderOnly: checked })}
                  />
                  <Label htmlFor="edit-isFirstOrderOnly">First Order Only</Label>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={handleUpdate}
                disabled={updateMutation.isPending || !formData.code.trim() || !formData.name.trim() || !formData.validFrom || !formData.validUntil}
              >
                {updateMutation.isPending ? 'Updating...' : 'Update Discount Code'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the discount code
                and remove it from our servers.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setDiscountCodeToDelete(null)}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? 'Deleting...' : 'Delete Discount Code'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AdminLayout>
  )
}
