# 🔔 GrocEase Notification System

A comprehensive real-time notification system for order updates in the GrocEase grocery delivery application.

## 🎯 Overview

This notification system provides real-time order status updates to customers via mobile push notifications and to administrators via browser notifications and real-time dashboard updates.

### Key Features

- **📱 Mobile Push Notifications**: FCM-powered notifications for order status updates
- **🖥️ Admin Real-time Updates**: WebSocket-based real-time order notifications for administrators
- **📊 Notification History**: Complete audit trail of all notifications sent
- **🔄 Automatic Triggers**: Notifications automatically sent when order status changes
- **⚡ High Performance**: Asynchronous processing with proper error handling

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Mobile App    │    │     Backend      │    │  Admin Panel    │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │Expo Notif.  │◄┼────┼─┤ FCM Service  │ │    │ │ WebSocket   │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ │ Client      │ │
│                 │    │                  │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │                 │
│ │Device Token │─┼────┼─┤ Notification │ │    │ ┌─────────────┐ │
│ │Registration │ │    │ │ Service      │ │    │ │ Browser     │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ │ Notifications│ │
└─────────────────┘    │                  │    │ └─────────────┘ │
                       │ ┌──────────────┐ │    └─────────────────┘
                       │ │ Order        │ │
                       │ │ Service      │ │
                       │ └──────────────┘ │
                       │                  │
                       │ ┌──────────────┐ │
                       │ │ PostgreSQL   │ │
                       │ │ Database     │ │
                       │ └──────────────┘ │
                       └──────────────────┘
```

## 🚀 Quick Start

### 1. Backend Setup

```bash
# Navigate to backend directory
cd grocease-backend

# Configure Firebase
cp firebase-service-account.json.example firebase-service-account.json
# Edit the file with your Firebase credentials

# Update application.yml
firebase:
  enabled: true
  config:
    file: firebase-service-account.json

# Start the backend
./mvnw spring-boot:run
```

### 2. Mobile App Setup

```bash
# Navigate to mobile app directory
cd grocease

# Install dependencies (if not already installed)
npm install

# Start the development server
npm start
```

### 3. Admin Panel Setup

```bash
# Navigate to admin panel directory
cd grocease-admin-panel

# Install dependencies (if not already installed)
npm install

# Start the development server
npm run dev
```

## 📋 Configuration

### Environment Variables

#### Backend (.env or application.yml)
```yaml
firebase:
  enabled: true
  config:
    file: firebase-service-account.json

spring:
  datasource:
    url: *****************************************
    username: ${DB_USERNAME:grocease}
    password: ${DB_PASSWORD:password}
```

#### Mobile App (.env)
```bash
EXPO_PROJECT_ID=your-expo-project-id
API_BASE_URL=http://localhost:8080
```

#### Admin Panel (.env.local)
```bash
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080/ws/admin
```

## 🔧 Key Components

### Backend Components

1. **NotificationService**: Core service for sending FCM notifications
2. **NotificationTriggerService**: Handles automatic notification triggers
3. **OrderService**: Manages order status updates and triggers notifications
4. **NotificationController**: REST endpoints for notification management
5. **FirebaseConfig**: Firebase initialization and configuration

### Mobile App Components

1. **useNotifications**: Hook for managing push notifications
2. **NotificationService**: Service for device token registration
3. **useOrderNotifications**: Hook for handling order-specific notifications

### Admin Panel Components

1. **useRealtimeOrders**: Hook for WebSocket-based real-time updates
2. **OrderNotificationBell**: UI component for notification display
3. **OrderStatusUpdate**: Component for updating order status

## 📊 Database Schema

### Key Tables

```sql
-- Device tokens for push notifications
CREATE TABLE user_device_tokens (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL REFERENCES users(id),
    device_token VARCHAR(255) NOT NULL UNIQUE,
    device_type VARCHAR(50),
    device_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notification history and audit trail
CREATE TABLE notification_history (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    title VARCHAR(255) NOT NULL,
    message TEXT,
    type VARCHAR(50) NOT NULL,
    device_token VARCHAR(255),
    status VARCHAR(50) NOT NULL,
    firebase_message_id VARCHAR(255),
    error_message TEXT,
    is_broadcast BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Order status history
CREATE TABLE order_status_history (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT NOT NULL REFERENCES orders(id),
    from_status VARCHAR(50),
    to_status VARCHAR(50) NOT NULL,
    changed_by VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 Notification Flow

### Customer Order Status Updates

1. **Order Status Change**: Admin updates order status in admin panel
2. **Trigger Service**: `NotificationTriggerService.sendOrderStatusNotification()` is called
3. **FCM Notification**: Notification sent via Firebase Cloud Messaging
4. **Mobile App**: Customer receives push notification
5. **History Logging**: Notification logged in `notification_history` table

### Admin New Order Notifications

1. **New Order**: Customer places order via mobile app
2. **Order Creation**: Order saved to database with CONFIRMED status
3. **Admin Notification**: `NotificationTriggerService.sendNewOrderNotificationToAdmins()` is called
4. **Multi-channel Delivery**:
   - FCM push notification to admin mobile devices
   - WebSocket message to admin panel browsers
   - Browser notification in admin panel

## 🧪 Testing

### Manual Testing

1. **Test Customer Notifications**:
   ```bash
   # Update order status via admin panel
   # Check mobile app for push notification
   # Verify notification history in database
   ```

2. **Test Admin Notifications**:
   ```bash
   # Place order via mobile app
   # Check admin panel for real-time notification
   # Verify browser notification appears
   ```

### API Testing

```bash
# Test device token registration
curl -X POST http://localhost:8080/notifications/register-token \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "deviceToken=ExponentPushToken[xxx]&deviceType=android"

# Test order status update
curl -X PUT http://localhost:8080/admin/orders/1/status \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status":"CONFIRMED","notes":"Order confirmed"}'
```

## 🔍 Monitoring & Debugging

### Log Monitoring

```bash
# Backend logs
tail -f grocease-backend/logs/application.log | grep -i notification

# Check notification history
SELECT * FROM notification_history ORDER BY created_at DESC LIMIT 10;

# Check active device tokens
SELECT COUNT(*) FROM user_device_tokens WHERE is_active = true;
```

### Common Issues

1. **Firebase Not Initialized**: Check service account JSON file
2. **No Device Tokens**: Ensure mobile app registers tokens
3. **Notifications Not Received**: Verify FCM configuration and permissions
4. **WebSocket Connection Failed**: Check admin panel WebSocket URL

## 📚 Documentation

- [Complete Setup Guide](./ORDER_NOTIFICATION_SETUP_GUIDE.md)
- [API Documentation](./API_DOCUMENTATION.md)
- [Firebase Setup Guide](./FIREBASE_SETUP.md)
- [Troubleshooting Guide](./TROUBLESHOOTING.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**For detailed implementation instructions, see [ORDER_NOTIFICATION_SETUP_GUIDE.md](./ORDER_NOTIFICATION_SETUP_GUIDE.md)**
