package com.grocease.controller;

import com.grocease.dto.ApiResponse;
import com.grocease.dto.auth.*;
import com.grocease.entity.OtpToken;
import com.grocease.service.AuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Slf4j
public class AuthController {

    private final AuthService authService;

    @PostMapping("/register")
    public ResponseEntity<ApiResponse<AuthResponse>> register(@Valid @RequestBody RegisterRequest request) {
        log.info("Registration attempt for email: {}", request.getEmail());
        AuthResponse response = authService.register(request);
        return ResponseEntity.ok(ApiResponse.success(response, "User registered successfully"));
    }

    @PostMapping("/login")
    public ResponseEntity<ApiResponse<AuthResponse>> login(@Valid @RequestBody LoginRequest request) {
        log.info("Login attempt for email: {}", request.getEmail());
        AuthResponse response = authService.login(request);
        return ResponseEntity.ok(ApiResponse.success(response, "Login successful"));
    }

    @PostMapping("/forgot-password")
    public ResponseEntity<ApiResponse<String>> forgotPassword(@Valid @RequestBody ForgotPasswordRequest request) {
        log.info("Forgot password request for email: {}", request.getEmail());
        authService.forgotPassword(request);
        return ResponseEntity.ok(ApiResponse.success("Password reset OTP sent to your email", "OTP sent successfully"));
    }

    @PostMapping("/reset-password")
    public ResponseEntity<ApiResponse<String>> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        log.info("Password reset attempt");
        authService.resetPassword(request);
        return ResponseEntity.ok(ApiResponse.success("Password reset successful", "Password has been reset successfully"));
    }

    @PostMapping("/verify-otp")
    public ResponseEntity<ApiResponse<AuthResponse>> verifyOtp(@Valid @RequestBody OtpVerificationRequest request) {
        log.info("OTP verification attempt for email: {} and type: {}", request.getEmail(), request.getType());
        AuthResponse response = authService.verifyOtp(request);

        if (response != null) {
            // Email verification successful - return tokens
            return ResponseEntity.ok(ApiResponse.success(response, "Email verified successfully. You are now logged in."));
        } else {
            // Other OTP types (like password reset) - return success message only
            return ResponseEntity.ok(ApiResponse.success(null, "OTP verified successfully"));
        }
    }

    @PostMapping("/resend-otp")
    public ResponseEntity<ApiResponse<String>> resendOtp(
            @RequestParam String email,
            @RequestParam OtpToken.OtpType type) {
        log.info("Resend OTP request for email: {} and type: {}", email, type);
        authService.resendOtp(email, type);
        return ResponseEntity.ok(ApiResponse.success("OTP sent successfully", "New OTP has been sent to your email"));
    }
}
