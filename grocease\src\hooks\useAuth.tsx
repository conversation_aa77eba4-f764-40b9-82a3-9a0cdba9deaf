import React, { createContext, useContext, useEffect, useState } from 'react';
import { 
  AuthState, 
  AuthUser, 
  LoginCredentials, 
  RegisterData, 
  ForgotPasswordData, 
  ResetPasswordData, 
  OTPVerificationData 
} from '../types';
import { authApi } from '../services/authApi';
import { StorageService } from '../services/storage';
import { logAuth } from '../config/logging';

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; message: string }>;
  register: (userData: RegisterData) => Promise<{ success: boolean; message: string; data?: AuthResponse }>;
  logout: () => Promise<void>;
  forgotPassword: (data: ForgotPasswordData) => Promise<{ success: boolean; message: string }>;
  verifyOTP: (data: OTPVerificationData) => Promise<{ success: boolean; message: string; token?: string }>;
  resetPassword: (data: ResetPasswordData) => Promise<{ success: boolean; message: string }>;
  resendOTP: (email: string, type: string) => Promise<{ success: boolean; message: string }>;
  refreshAuthToken: () => Promise<void>;
  updateUser: (userData: Partial<AuthUser>) => void;
  updateProfile: (profileData: { name: string; email: string; phone: string; avatar: string }) => Promise<void>;
  completeOnboarding: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isAuthenticated: false,
    isLoading: true,
    hasCompletedOnboarding: false,
  });

  // Initialize auth state from storage
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const tokens = await StorageService.getTokens();
      const userData = await StorageService.getUserData();
      const hasCompletedOnboarding = await StorageService.isOnboardingCompleted();

      if (tokens && userData) {
        // Verify token is still valid (in real app, check expiration)
        try {
          const response = await authApi.getCurrentUser(tokens.token);
          if (response.success) {
            setAuthState({
              user: response.data,
              token: tokens.token,
              isAuthenticated: true,
              isLoading: false,
              hasCompletedOnboarding,
            });
            return;
          }
        } catch (error) {
          // Token might be expired, try to refresh
          try {
            const refreshResponse = await authApi.refreshToken(tokens.refreshToken);
            if (refreshResponse.success) {
              await StorageService.storeTokens(
                refreshResponse.data.token,
                refreshResponse.data.refreshToken
              );
              setAuthState({
                user: userData,
                token: refreshResponse.data.token,
                isAuthenticated: true,
                isLoading: false,
                hasCompletedOnboarding,
              });
              return;
            }
          } catch (refreshError) {
            // Refresh failed, clear stored data
            await StorageService.clearAllData();
          }
        }
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
    }

    setAuthState({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      hasCompletedOnboarding: false,
    });
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await authApi.login(credentials);
      
      if (response.success) {
        const { user, token, refreshToken } = response.data;
        
        // Store tokens and user data
        await StorageService.storeTokens(token, refreshToken);
        await StorageService.storeUserData(user);
        
        // Check if user has completed onboarding
        const hasCompletedOnboarding = await StorageService.isOnboardingCompleted();

        setAuthState({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
          hasCompletedOnboarding,
        });

        logAuth('Login successful, auth state updated', { isAuthenticated: true, user: user.email });

        return { success: true, message: response.message || 'Login successful' };
      } else {
        return { success: false, message: response.message || 'Login failed' };
      }
    } catch (error) {
      return { success: false, message: 'Network error. Please try again.' };
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      const response = await authApi.register(userData);
      
      if (response.success) {
        const { user, token, refreshToken } = response.data;
        
        // Store tokens and user data
        await StorageService.storeTokens(token, refreshToken);
        await StorageService.storeUserData(user);
        
        // For registered users, skip onboarding and go directly to main app
        // Onboarding is only for first-time app users, not after registration
        await StorageService.setOnboardingCompleted(true);

        setAuthState({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
          hasCompletedOnboarding: true,
        });
        
        return { success: true, message: response.message || 'Registration successful', data: response.data };
      } else {
        return { success: false, message: response.message || 'Registration failed' };
      }
    } catch (error) {
      return { success: false, message: 'Network error. Please try again.' };
    }
  };

  const logout = async () => {
    try {
      await StorageService.clearAllData();
      setAuthState({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        hasCompletedOnboarding: false,
      });
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const forgotPassword = async (data: ForgotPasswordData) => {
    try {
      const response = await authApi.forgotPassword(data);
      return { 
        success: response.success, 
        message: response.message || (response.success ? 'OTP sent' : 'Failed to send OTP')
      };
    } catch (error) {
      return { success: false, message: 'Network error. Please try again.' };
    }
  };

  const verifyOTP = async (data: OTPVerificationData) => {
    try {
      const response = await authApi.verifyOTP(data);
      return { 
        success: response.success, 
        message: response.message || (response.success ? 'OTP verified' : 'Invalid OTP'),
        token: response.data?.token
      };
    } catch (error) {
      return { success: false, message: 'Network error. Please try again.' };
    }
  };

  const resetPassword = async (data: ResetPasswordData) => {
    try {
      const response = await authApi.resetPassword(data);
      return { 
        success: response.success, 
        message: response.message || (response.success ? 'Password reset' : 'Failed to reset password')
      };
    } catch (error) {
      return { success: false, message: 'Network error. Please try again.' };
    }
  };

  const resendOTP = async (email: string, type: string) => {
    try {
      const response = await authApi.resendOTP(email, type);
      return { 
        success: response.success, 
        message: response.message || (response.success ? 'OTP sent' : 'Failed to send OTP')
      };
    } catch (error) {
      return { success: false, message: 'Network error. Please try again.' };
    }
  };

  const refreshAuthToken = async () => {
    try {
      const tokens = await StorageService.getTokens();
      if (tokens) {
        const response = await authApi.refreshToken(tokens.refreshToken);
        if (response.success) {
          await StorageService.storeTokens(
            response.data.token,
            response.data.refreshToken
          );
          setAuthState(prev => ({
            ...prev,
            token: response.data.token,
          }));
        }
      }
    } catch (error) {
      console.error('Error refreshing token:', error);
    }
  };

  const updateUser = (userData: Partial<AuthUser>) => {
    setAuthState(prev => ({
      ...prev,
      user: prev.user ? { ...prev.user, ...userData } : null,
    }));
  };

  const updateProfile = async (profileData: { name: string; email: string; phone: string; avatar: string }) => {
    try {
      // In a real app, this would make an API call
      // const response = await authApi.updateProfile(profileData);

      // For now, just update the local state
      updateUser(profileData);

      // Save to storage
      if (authState.user) {
        const updatedUser = { ...authState.user, ...profileData };
        await StorageService.storeUserData(updatedUser);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  };

  const completeOnboarding = async () => {
    try {
      await StorageService.setOnboardingCompleted(true);
      setAuthState(prev => ({
        ...prev,
        hasCompletedOnboarding: true,
      }));
      logAuth('Onboarding completed');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    ...authState,
    login,
    register,
    logout,
    forgotPassword,
    verifyOTP,
    resetPassword,
    resendOTP,
    refreshAuthToken,
    updateUser,
    updateProfile,
    completeOnboarding,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
