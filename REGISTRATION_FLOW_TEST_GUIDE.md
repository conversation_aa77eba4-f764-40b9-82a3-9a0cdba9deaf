# 🧪 Registration Flow Test Guide

This guide helps you test the corrected registration flow: **Registration → OTP Verification → Authentication → Home Screen**

## 🔧 Setup Required

### Backend Setup
1. **Enable Email Verification** (already done)
   ```yaml
   # In application.yml
   app:
     features:
       email-verification:
         enabled: true
   ```

2. **Start Backend**
   ```bash
   cd grocease-backend
   ./mvnw spring-boot:run
   ```

3. **Check Email Service** (for OTP delivery)
   - Ensure email service is configured in application.yml
   - Check logs for email sending confirmation

### Mobile App Setup
1. **Start Mobile App**
   ```bash
   cd grocease
   npm start
   ```

## 📱 Testing Steps

### Step 1: Registration
1. **Open the app** and navigate to Register screen
2. **Fill in registration form**:
   - Name: Test User
   - Email: <EMAIL>
   - Phone: +**********
   - Password: password123
   - Confirm Password: password123
   - Accept Terms: ✓

3. **Tap "Create Account"**

### Expected Result ✅
- **Success Alert**: "Registration successful. Please check your email and verify your account to continue."
- **Navigation**: Should navigate to OTP Verification screen
- **User State**: Should NOT be authenticated yet
- **Backend**: Should send OTP email (check logs)

### Step 2: OTP Verification
1. **Check email** for OTP code (or check backend logs for OTP)
2. **Enter the 6-digit OTP** in the verification screen
3. **Tap "Verify"**

### Expected Result ✅
- **Success Alert**: "Email verified successfully! Welcome to GroceEase."
- **Authentication**: User should now be authenticated
- **Navigation**: Should automatically navigate to Home screen (Main tab navigator)
- **Onboarding**: Should be skipped (as per memory requirement)

## 🔍 Verification Points

### Backend Verification
```sql
-- Check user was created but not initially verified
SELECT name, email, is_email_verified, is_active FROM users WHERE email = '<EMAIL>';

-- Check OTP was generated
SELECT token, type, is_used, expires_at FROM otp_tokens WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>');

-- After OTP verification, user should be verified
SELECT name, email, is_email_verified FROM users WHERE email = '<EMAIL>';
```

### Mobile App Verification
1. **Check AsyncStorage**:
   ```javascript
   // In React Native Debugger or logs
   AsyncStorage.getItem('auth_token') // Should be null before OTP verification
   AsyncStorage.getItem('auth_token') // Should have token after OTP verification
   AsyncStorage.getItem('onboarding_completed') // Should be 'true'
   ```

2. **Check Auth State**:
   - Before OTP: `isAuthenticated: false`
   - After OTP: `isAuthenticated: true, hasCompletedOnboarding: true`

## 🐛 Troubleshooting

### Issue: "Email verification disabled" message
**Solution**: Check application.yml has `email-verification.enabled: true`

### Issue: OTP not received
**Solutions**:
1. Check backend logs for email sending errors
2. Check email service configuration
3. For testing, find OTP in backend logs:
   ```bash
   grep -i "OTP sent" logs/application.log
   ```

### Issue: User authenticated immediately after registration
**Problem**: Email verification is disabled or backend returning tokens
**Solution**: Verify application.yml and AuthService.register() method

### Issue: Navigation doesn't work after OTP verification
**Problem**: Auth state not updating properly
**Solution**: Check useAuth.tsx verifyOTP method and setAuthState call

### Issue: Onboarding screen appears after OTP verification
**Problem**: hasCompletedOnboarding not set to true
**Solution**: Check StorageService.setOnboardingCompleted(true) call

## 📊 Test Scenarios

### Scenario 1: Successful Registration Flow
1. Register → OTP sent → Verify OTP → Home Screen ✅

### Scenario 2: Invalid OTP
1. Register → OTP sent → Enter wrong OTP → Error message → Try again ✅

### Scenario 3: Expired OTP
1. Register → Wait 10+ minutes → Enter OTP → "OTP expired" error ✅

### Scenario 4: Resend OTP
1. Register → OTP sent → Tap "Resend OTP" → New OTP sent ✅

### Scenario 5: Email Verification Disabled
1. Set `email-verification.enabled: false`
2. Register → Immediate authentication → Home Screen ✅

## 🔄 Reset Test Environment

To test multiple times:

1. **Delete test user from database**:
   ```sql
   DELETE FROM otp_tokens WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>');
   DELETE FROM users WHERE email = '<EMAIL>';
   ```

2. **Clear mobile app storage**:
   ```javascript
   // In app or React Native Debugger
   AsyncStorage.clear();
   ```

3. **Restart mobile app** to reset auth state

## ✅ Success Criteria

The registration flow is working correctly when:

1. ✅ Registration does NOT authenticate user immediately
2. ✅ OTP is sent to user's email
3. ✅ OTP verification screen appears after registration
4. ✅ Valid OTP verification authenticates the user
5. ✅ User navigates directly to Home screen (skips onboarding)
6. ✅ Invalid OTP shows appropriate error message
7. ✅ User remains authenticated after app restart

## 📝 Notes

- **Onboarding Skip**: As per memory, users should skip onboarding after registration/login
- **Email Service**: Ensure email service is properly configured for OTP delivery
- **Token Security**: Tokens are only issued after successful email verification
- **Error Handling**: All error cases should be handled gracefully with user-friendly messages

---

**After successful testing, the registration flow will work as intended: Registration → OTP Verification → Home Screen**
