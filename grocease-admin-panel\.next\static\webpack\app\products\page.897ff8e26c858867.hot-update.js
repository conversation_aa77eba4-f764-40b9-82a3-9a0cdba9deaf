"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/AdminLayout */ \"(app-pages-browser)/./src/components/layout/AdminLayout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/image-upload */ \"(app-pages-browser)/./src/components/ui/image-upload.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Edit,Eye,Package,Plus,Search,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_19__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ALL\");\n    const [featuredFilter, setFeaturedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"ALL\");\n    const [isCreateDialogOpen, setIsCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditDialogOpen, setIsEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingProduct, setEditingProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDeleteDialog, setShowDeleteDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [productToDelete, setProductToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        image: \"\",\n        categoryId: 0,\n        unit: \"\",\n        inStock: true,\n        rating: 0,\n        reviewCount: 0,\n        tags: [],\n        isFeatured: false,\n        stockQuantity: 0\n    });\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQueryClient)();\n    const { data: productsData, isLoading } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useQuery)({\n        queryKey: [\n            \"products\",\n            page,\n            pageSize,\n            search,\n            categoryFilter,\n            featuredFilter\n        ],\n        queryFn: ()=>_lib_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].getProducts(page, pageSize, categoryFilter === \"ALL\" ? undefined : categoryFilter, search || undefined)\n    });\n    // Reset page when page size changes\n    const handlePageSizeChange = (newPageSize)=>{\n        setPageSize(parseInt(newPageSize));\n        setPage(0);\n    };\n    const { data: categories } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useQuery)({\n        queryKey: [\n            \"categories\"\n        ],\n        queryFn: ()=>_lib_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].getCategories()\n    });\n    const createMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_22__.useMutation)({\n        mutationFn: (data)=>_lib_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].createProduct(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            setIsCreateDialogOpen(false);\n            resetForm();\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.success(\"Product created successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to create product\");\n        }\n    });\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_22__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _lib_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].updateProduct(id, data);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            setIsEditDialogOpen(false);\n            setEditingProduct(null);\n            resetForm();\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.success(\"Product updated successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to update product\");\n        }\n    });\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_22__.useMutation)({\n        mutationFn: (id)=>_lib_api__WEBPACK_IMPORTED_MODULE_15__[\"default\"].deleteProduct(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"products\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.success(\"Product deleted successfully\");\n        },\n        onError: (error)=>{\n            var _error_response_data, _error_response;\n            sonner__WEBPACK_IMPORTED_MODULE_17__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to delete product\");\n        }\n    });\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            image: \"\",\n            categoryId: 0,\n            unit: \"\",\n            inStock: true,\n            rating: 0,\n            reviewCount: 0,\n            tags: [],\n            isFeatured: false,\n            stockQuantity: 0\n        });\n    };\n    const handleCreate = ()=>{\n        createMutation.mutate(formData);\n    };\n    const handleEdit = (product)=>{\n        var _categories_find;\n        setEditingProduct(product);\n        // Find the category ID from the category name\n        const categoryId = (categories === null || categories === void 0 ? void 0 : (_categories_find = categories.find((cat)=>cat.name === product.category)) === null || _categories_find === void 0 ? void 0 : _categories_find.id) || \"0\";\n        setFormData({\n            name: product.name,\n            description: product.description,\n            price: product.price,\n            originalPrice: product.originalPrice || 0,\n            discount: product.discount || 0,\n            image: product.image,\n            categoryId: parseInt(categoryId),\n            unit: product.unit,\n            inStock: product.inStock,\n            rating: product.rating,\n            reviewCount: product.reviewCount,\n            tags: product.tags || [],\n            isFeatured: product.isFeatured || false,\n            stockQuantity: product.stockQuantity || 0\n        });\n        setIsEditDialogOpen(true);\n    };\n    const handleUpdate = ()=>{\n        if (editingProduct) {\n            updateMutation.mutate({\n                id: editingProduct.id,\n                data: formData\n            });\n        }\n    };\n    const handleDelete = (id)=>{\n        setProductToDelete(id);\n        setShowDeleteDialog(true);\n    };\n    const confirmDelete = ()=>{\n        if (productToDelete) {\n            deleteMutation.mutate(productToDelete);\n            setShowDeleteDialog(false);\n            setProductToDelete(null);\n        }\n    };\n    const filteredProducts = ((productsData === null || productsData === void 0 ? void 0 : productsData.data) || []).filter((product)=>{\n        if (featuredFilter === \"FEATURED\") return product.isFeatured;\n        if (featuredFilter === \"REGULAR\") return !product.isFeatured;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold tracking-tight\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Manage your product catalog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                            open: isCreateDialogOpen,\n                            onOpenChange: setIsCreateDialogOpen,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: resetForm,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Add Product\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Filter Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: \"Search and filter products by category\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"absolute left-3 top-3 h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                    placeholder: \"Search products...\",\n                                                    value: search,\n                                                    onChange: (e)=>setSearch(e.target.value),\n                                                    className: \"pl-10\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        value: categoryFilter,\n                                        onValueChange: setCategoryFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-48\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: \"Filter by category\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"ALL\",\n                                                        children: \"All Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                            value: category.id,\n                                                            children: category.name\n                                                        }, category.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        value: featuredFilter,\n                                        onValueChange: setFeaturedFilter,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-40\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: \"Featured\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"ALL\",\n                                                        children: \"All Products\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"FEATURED\",\n                                                        children: \"Featured Only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"REGULAR\",\n                                                        children: \"Regular Only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                        value: pageSize.toString(),\n                                        onValueChange: handlePageSizeChange,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                className: \"w-32\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                    placeholder: \"Page size\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"5\",\n                                                        children: \"5 per page\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"10\",\n                                                        children: \"10 per page\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"20\",\n                                                        children: \"20 per page\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"50\",\n                                                        children: \"50 per page\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                        value: \"100\",\n                                                        children: \"100 per page\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: \"Products List\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                    children: [\n                                        (productsData === null || productsData === void 0 ? void 0 : productsData.pagination.total) || 0,\n                                        \" total products\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Product\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Price\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Stock\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Rating\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n                                                children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-shrink-0\",\n                                                                            children: product.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_18___default()), {\n                                                                                src: product.image,\n                                                                                alt: product.name,\n                                                                                width: 50,\n                                                                                height: 50,\n                                                                                className: \"rounded-lg object-cover\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 33\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-12 h-12 bg-muted rounded-lg flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-6 w-6 text-muted-foreground\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 353,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium\",\n                                                                                    children: product.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 358,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground\",\n                                                                                    children: product.unit\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 359,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: product.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"font-medium\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_16__.formatCurrency)(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 370,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground line-through\",\n                                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_16__.formatCurrency)(product.originalPrice)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-medium \".concat(!product.inStock ? \"text-red-600\" : \"\"),\n                                                                        children: product.inStock ? \"In Stock\" : \"Out of Stock\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-4 w-4 fill-yellow-400 text-yellow-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: product.rating\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 388,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: [\n                                                                                \"(\",\n                                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_16__.formatNumber)(product.reviewCount),\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: product.isFeatured ? \"default\" : \"outline\",\n                                                                    children: product.isFeatured ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-3 w-3 fill-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Featured\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 31\n                                                                    }, this) : \"Regular\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: product.inStock ? \"default\" : \"destructive\",\n                                                                    children: product.inStock ? \"Active\" : \"Inactive\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_19___default()), {\n                                                                            href: \"/products/\".concat(product.id),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 418,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 417,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleEdit(product),\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>handleDelete(product.id),\n                                                                            disabled: deleteMutation.isPending,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, product.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this),\n                                    (productsData === null || productsData === void 0 ? void 0 : productsData.pagination) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: [\n                                                    \"Showing \",\n                                                    page * pageSize + 1,\n                                                    \" to \",\n                                                    Math.min((page + 1) * pageSize, productsData.pagination.total),\n                                                    \" of\",\n                                                    \" \",\n                                                    productsData.pagination.total,\n                                                    \" products\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setPage(page - 1),\n                                                        disabled: page === 0,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Previous\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: [\n                                                            \"Page \",\n                                                            page + 1,\n                                                            \" of \",\n                                                            productsData.pagination.totalPages\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setPage(page + 1),\n                                                        disabled: page >= productsData.pagination.totalPages - 1,\n                                                        children: [\n                                                            \"Next\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Edit_Eye_Package_Plus_Search_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                    open: isCreateDialogOpen,\n                    onOpenChange: setIsCreateDialogOpen,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogContent, {\n                        className: \"sm:max-w-[600px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTitle, {\n                                        children: \"Create Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogDescription, {\n                                        children: \"Add a new product to your catalog.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 484,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4 py-4 max-h-[60vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"name\",\n                                                children: \"Product Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                placeholder: \"Product name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"description\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                id: \"description\",\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                placeholder: \"Product description\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"price\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"price\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"originalPrice\",\n                                                        children: \"Original Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"originalPrice\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        value: formData.originalPrice,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                originalPrice: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"discount\",\n                                                        children: \"Discount (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"discount\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        value: formData.discount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"unit\",\n                                                        children: \"Unit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"unit\",\n                                                        value: formData.unit,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                unit: e.target.value\n                                                            }),\n                                                        placeholder: \"kg, piece, liter, etc.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"category\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                value: formData.categoryId.toString(),\n                                                onValueChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        categoryId: parseInt(value)\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                            placeholder: \"Select a category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                        children: categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: category.id,\n                                                                children: category.name\n                                                            }, category.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                children: \"Product Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__.ImageUpload, {\n                                                value: formData.image,\n                                                onChange: (url)=>setFormData({\n                                                        ...formData,\n                                                        image: url\n                                                    }),\n                                                type: \"product\",\n                                                placeholder: \"Upload product image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"rating\",\n                                                        children: \"Rating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"rating\",\n                                                        type: \"number\",\n                                                        step: \"0.1\",\n                                                        min: \"0\",\n                                                        max: \"5\",\n                                                        value: formData.rating,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                rating: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"reviewCount\",\n                                                        children: \"Review Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"reviewCount\",\n                                                        type: \"number\",\n                                                        value: formData.reviewCount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                reviewCount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"stockQuantity\",\n                                                children: \"Stock Quantity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"stockQuantity\",\n                                                type: \"number\",\n                                                min: \"0\",\n                                                value: formData.stockQuantity || 0,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        stockQuantity: parseInt(e.target.value) || 0\n                                                    }),\n                                                placeholder: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"inStock\",\n                                                checked: formData.inStock,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        inStock: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"inStock\",\n                                                children: \"In Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"isFeatured\",\n                                                checked: formData.isFeatured || false,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        isFeatured: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"isFeatured\",\n                                                children: \"Featured Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogFooter, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleCreate,\n                                    disabled: createMutation.isPending || !formData.name.trim() || !formData.categoryId,\n                                    children: createMutation.isPending ? \"Creating...\" : \"Create Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 641,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.Dialog, {\n                    open: isEditDialogOpen,\n                    onOpenChange: setIsEditDialogOpen,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogContent, {\n                        className: \"sm:max-w-[600px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogTitle, {\n                                        children: \"Edit Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogDescription, {\n                                        children: \"Update the product information.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4 py-4 max-h-[60vh] overflow-y-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-name\",\n                                                children: \"Product Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"edit-name\",\n                                                value: formData.name,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        name: e.target.value\n                                                    }),\n                                                placeholder: \"Product name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 661,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-description\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                id: \"edit-description\",\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                placeholder: \"Product description\",\n                                                rows: 3\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-price\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-price\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        value: formData.price,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                price: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-originalPrice\",\n                                                        children: \"Original Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-originalPrice\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        value: formData.originalPrice,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                originalPrice: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 693,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-discount\",\n                                                        children: \"Discount (%)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 708,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-discount\",\n                                                        type: \"number\",\n                                                        step: \"1\",\n                                                        min: \"0\",\n                                                        max: \"100\",\n                                                        value: formData.discount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                discount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-unit\",\n                                                        children: \"Unit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-unit\",\n                                                        value: formData.unit,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                unit: e.target.value\n                                                            }),\n                                                        placeholder: \"kg, piece, liter, etc.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 722,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-category\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.Select, {\n                                                value: formData.categoryId.toString(),\n                                                onValueChange: (value)=>setFormData({\n                                                        ...formData,\n                                                        categoryId: parseInt(value)\n                                                    }),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectTrigger, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectValue, {\n                                                            placeholder: \"Select a category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectContent, {\n                                                        children: categories === null || categories === void 0 ? void 0 : categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_12__.SelectItem, {\n                                                                value: category.id,\n                                                                children: category.name\n                                                            }, category.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                                lineNumber: 741,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                children: \"Product Image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_image_upload__WEBPACK_IMPORTED_MODULE_10__.ImageUpload, {\n                                                value: formData.image,\n                                                onChange: (url)=>setFormData({\n                                                        ...formData,\n                                                        image: url\n                                                    }),\n                                                type: \"product\",\n                                                placeholder: \"Upload product image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 748,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-rating\",\n                                                        children: \"Rating\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-rating\",\n                                                        type: \"number\",\n                                                        step: \"0.1\",\n                                                        min: \"0\",\n                                                        max: \"5\",\n                                                        value: formData.rating,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                rating: parseFloat(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0.0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                        htmlFor: \"edit-reviewCount\",\n                                                        children: \"Review Count\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                        id: \"edit-reviewCount\",\n                                                        type: \"number\",\n                                                        value: formData.reviewCount,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                reviewCount: parseInt(e.target.value) || 0\n                                                            }),\n                                                        placeholder: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-stockQuantity\",\n                                                children: \"Stock Quantity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                                id: \"edit-stockQuantity\",\n                                                type: \"number\",\n                                                min: \"0\",\n                                                value: formData.stockQuantity || 0,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        stockQuantity: parseInt(e.target.value) || 0\n                                                    }),\n                                                placeholder: \"0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"edit-inStock\",\n                                                checked: formData.inStock,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        inStock: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-inStock\",\n                                                children: \"In Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                id: \"edit-isFeatured\",\n                                                checked: formData.isFeatured || false,\n                                                onCheckedChange: (checked)=>setFormData({\n                                                        ...formData,\n                                                        isFeatured: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                                htmlFor: \"edit-isFeatured\",\n                                                children: \"Featured Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_13__.DialogFooter, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleUpdate,\n                                    disabled: updateMutation.isPending || !formData.name.trim() || !formData.categoryId,\n                                    children: updateMutation.isPending ? \"Updating...\" : \"Update Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 653,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 652,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__.AlertDialog, {\n                    open: showDeleteDialog,\n                    onOpenChange: setShowDeleteDialog,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__.AlertDialogContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__.AlertDialogHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__.AlertDialogTitle, {\n                                        children: \"Are you absolutely sure?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__.AlertDialogDescription, {\n                                        children: \"This action cannot be undone. This will permanently delete the product and remove it from our servers.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 826,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 824,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__.AlertDialogFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__.AlertDialogCancel, {\n                                        onClick: ()=>setProductToDelete(null),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_14__.AlertDialogAction, {\n                                        onClick: confirmDelete,\n                                        className: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n                                        disabled: deleteMutation.isPending,\n                                        children: deleteMutation.isPending ? \"Deleting...\" : \"Delete Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 831,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 823,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 822,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n            lineNumber: 232,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\Grocery-app\\\\grocease-admin-panel\\\\src\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"fDatJdm2e7KZFk1FTTLcQB3Eoas=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_21__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.tsx\n"));

/***/ })

});